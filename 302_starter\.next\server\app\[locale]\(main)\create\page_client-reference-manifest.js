globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/(main)/create/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/(main)/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sidebar.tsx":{"*":{"id":"(ssr)/./src/components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-auth/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-auth/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-chat/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-chat/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-click-monitor/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-click-monitor/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-client/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-client/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-header/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-jotai/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-jotai/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-message/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-message/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-theme/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-theme/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/app-tooltip/index.tsx":{"*":{"id":"(ssr)/./src/components/global/app-tooltip/index.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\app\\[locale]\\(main)\\create\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx","name":"*","chunks":["app/[locale]/(main)/create/page","static/chunks/app/%5Blocale%5D/(main)/create/page.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\ui\\sidebar.tsx":{"id":"(app-pages-browser)/./src/components/ui/sidebar.tsx","name":"*","chunks":["app/[locale]/(main)/layout","static/chunks/app/%5Blocale%5D/(main)/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1\\node_modules\\next-intl\\dist\\esm\\navigation\\shared\\BaseLink.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1\\node_modules\\next-intl\\dist\\esm\\navigation\\shared\\LegacyBaseLink.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\node_modules\\.pnpm\\next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1\\node_modules\\next-intl\\dist\\esm\\shared\\NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-auth\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-auth/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-chat\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-chat/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-click-monitor\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-click-monitor/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-client\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-client/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-header\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-header/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-jotai\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-jotai/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-message\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-message/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-theme\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-theme/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\components\\global\\app-tooltip\\index.tsx":{"id":"(app-pages-browser)/./src/components/global/app-tooltip/index.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\code\\数字人\\302_starter\\src\\styles\\globals.css":{"id":"(app-pages-browser)/./src/styles/globals.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false}},"entryCSSFiles":{"D:\\code\\数字人\\302_starter\\src\\":[],"D:\\code\\数字人\\302_starter\\src\\app\\[locale]\\(main)\\layout":[],"D:\\code\\数字人\\302_starter\\src\\app\\[locale]\\layout":["static/css/app/[locale]/layout.css"],"D:\\code\\数字人\\302_starter\\src\\app\\[locale]\\(main)\\create\\page":[]}}