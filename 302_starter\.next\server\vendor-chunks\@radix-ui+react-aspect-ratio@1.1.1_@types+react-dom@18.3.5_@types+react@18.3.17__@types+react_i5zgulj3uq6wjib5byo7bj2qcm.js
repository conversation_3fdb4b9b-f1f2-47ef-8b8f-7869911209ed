"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-aspect-ratio@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react_i5zgulj3uq6wjib5byo7bj2qcm";
exports.ids = ["vendor-chunks/@radix-ui+react-aspect-ratio@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react_i5zgulj3uq6wjib5byo7bj2qcm"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-aspect-ratio@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react_i5zgulj3uq6wjib5byo7bj2qcm/node_modules/@radix-ui/react-aspect-ratio/dist/index.mjs":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-aspect-ratio@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react_i5zgulj3uq6wjib5byo7bj2qcm/node_modules/@radix-ui/react-aspect-ratio/dist/index.mjs ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AspectRatio: () => (/* binding */ AspectRatio),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/aspect-ratio/src/AspectRatio.tsx\n\n\n\nvar NAME = \"AspectRatio\";\nvar AspectRatio = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"div\",\n      {\n        style: {\n          // ensures inner element is contained\n          position: \"relative\",\n          // ensures padding bottom trick maths works\n          width: \"100%\",\n          paddingBottom: `${100 / ratio}%`\n        },\n        \"data-radix-aspect-ratio-wrapper\": \"\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,\n          {\n            ...aspectRatioProps,\n            ref: forwardedRef,\n            style: {\n              ...style,\n              // ensures children expand in ratio\n              position: \"absolute\",\n              top: 0,\n              right: 0,\n              bottom: 0,\n              left: 0\n            }\n          }\n        )\n      }\n    );\n  }\n);\nAspectRatio.displayName = NAME;\nvar Root = AspectRatio;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-aspect-ratio@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react_i5zgulj3uq6wjib5byo7bj2qcm/node_modules/@radix-ui/react-aspect-ratio/dist/index.mjs\n");

/***/ })

};
;