"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/(main)/create/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/index.ts\");\n/* harmony import */ var _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hooks/useCreatePage */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\");\n/* harmony import */ var _stores__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores */ \"(app-pages-browser)/./src/stores/index.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n/* harmony import */ var ky__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ky */ \"(app-pages-browser)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/env */ \"(app-pages-browser)/./src/env.ts\");\n/* harmony import */ var _constants_voices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/constants/voices */ \"(app-pages-browser)/./src/constants/voices.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nasync function fetchTTSProviders(apiKey) {\n    if (!apiKey) {\n        return;\n    }\n    try {\n        const response = await ky__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(_env__WEBPACK_IMPORTED_MODULE_7__.env.NEXT_PUBLIC_API_URL, \"/302/tts/provider\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"API调用失败: \".concat(response.status));\n        }\n        const result = await response.json();\n        return result;\n    } catch (err) {\n        const errorText = await err.response.text();\n        const errorData = JSON.parse(errorText);\n        if (errorData.error && errorData.error.err_code) {\n        // toast.error(() => ErrorToast(errorData.error.err_code));\n        } else {\n        // toast.error(\"获取供应商失败\");\n        }\n    }\n}\nconst CreatePage = ()=>{\n    _s();\n    const { state, updateSmallSelect, updateLargeSelect, updateTextContent } = (0,_hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage)();\n    const { apiKey } = _stores__WEBPACK_IMPORTED_MODULE_5__.store.get(_stores__WEBPACK_IMPORTED_MODULE_5__.appConfigAtom);\n    const [voiceStore, setVoiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__.voiceStoreAtom);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (async ()=>{\n            const providerData = await fetchTTSProviders(apiKey);\n            if (!providerData) {\n                return;\n            }\n            const { provider_list } = providerData;\n            // 处理 doubao 数据\n            const doubaoProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"doubao\");\n            if (doubaoProvider) {\n                const doubaoVoiceList = doubaoProvider.req_params_info.voice_list || [];\n                const doubaoVoiceOptions = doubaoVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Doubao children\n                const doubaoVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Doubao\");\n                if (doubaoVoice) {\n                    doubaoVoice.children = doubaoVoiceOptions;\n                }\n            }\n            // 处理 fish 数据\n            const fishProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"fish\");\n            if (fishProvider) {\n                const fishVoiceList = fishProvider.req_params_info.voice_list || [];\n                const fishVoiceOptions = fishVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.name || voice.voice,\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 FishAudio children\n                const fishVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"fish\");\n                if (fishVoice) {\n                    fishVoice.children = fishVoiceOptions;\n                }\n            }\n            // 处理 minimax 数据\n            const minimaxProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"minimaxi\");\n            if (minimaxProvider) {\n                const minimaxVoiceList = minimaxProvider.req_params_info.voice_list || [];\n                const minimaxVoiceOptions = minimaxVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Minimax children\n                const minimaxVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Minimaxi\");\n                if (minimaxVoice) {\n                    minimaxVoice.children = minimaxVoiceOptions;\n                }\n            }\n            // // 处理 dubbingxi 数据\n            // const dubbingxiProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"dubbingx\"\n            // );\n            // if (dubbingxiProvider) {\n            //   const dubbingxiVoiceList =\n            //     dubbingxiProvider.req_params_info.voice_list || [];\n            //   const dubbingxiVoiceOptions: VoiceOption[] = dubbingxiVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: `${voice.name} (${t(voice.gender.toLowerCase())})`,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 dubbingxi children\n            //   const dubbingxiVoice = voices.find((v) => v.key === \"dubbingx\");\n            //   if (dubbingxiVoice) {\n            //     dubbingxiVoice.children = dubbingxiVoiceOptions;\n            //   }\n            // }\n            // // 处理 elevenlabs 数据\n            // const elevenlabsProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"elevenlabs\"\n            // );\n            // if (elevenlabsProvider) {\n            //   const elevenlabsVoiceList =\n            //     elevenlabsProvider.req_params_info.voice_list || [];\n            //   const elevenlabsVoiceOptions: VoiceOption[] = elevenlabsVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: voice.name || voice.voice,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 elevenlabs children\n            //   const elevenlabsVoice = voices.find((v) => v.key === \"elevenlabs\");\n            //   if (elevenlabsVoice) {\n            //     elevenlabsVoice.children = elevenlabsVoiceOptions;\n            //   }\n            // }\n            // 处理 openai 数据\n            const openAiProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"openai\");\n            if (openAiProvider) {\n                const openAiVoiceList = openAiProvider.req_params_info.voice_list || [];\n                // 只保留指定的几个音色\n                const allowedVoices = [\n                    \"alloy\",\n                    \"echo\",\n                    \"fable\",\n                    \"onyx\",\n                    \"nova\",\n                    \"shimmer\"\n                ];\n                const filteredOpenAiVoiceList = openAiVoiceList.filter((voice)=>allowedVoices.includes(voice.voice.toLowerCase()));\n                const openAiVoiceOptions = filteredOpenAiVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.gender ? \"\".concat(voice.name.charAt(0).toUpperCase() + voice.name.slice(1), \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\") : voice.name.charAt(0).toUpperCase() + voice.name.slice(1),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 openai children\n                const openAiVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"OpenAI\");\n                if (openAiVoice) {\n                    openAiVoice.children = openAiVoiceOptions;\n                }\n            }\n            // 如果需要持久化，可以更新到 store\n            setVoiceStore((prev)=>({\n                    ...prev,\n                    voiceList: _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices\n                }));\n        })();\n    }, [\n        apiKey\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"mx-auto w-full max-w-6xl p-6 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full flex-col gap-6 lg:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-2/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.VideoPreviewFrame, {\n                            coverImage: state.coverImageUrl,\n                            placeholder: \"视频封面预览\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-3/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.ConfigurationPanel, {\n                            smallSelectValue: state.smallSelectValue,\n                            largeSelectValue: state.largeSelectValue,\n                            textContent: state.textContent,\n                            onSmallSelectChange: updateSmallSelect,\n                            onLargeSelectChange: updateLargeSelect,\n                            onTextChange: updateTextContent\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePage, \"24XdJc2JrL93lOcodoH/WY0i7U8=\", false, function() {\n    return [\n        _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage,\n        jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom\n    ];\n});\n_c = CreatePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreatePage);\nvar _c;\n$RefreshReg$(_c, \"CreatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx\n"));

/***/ })

});