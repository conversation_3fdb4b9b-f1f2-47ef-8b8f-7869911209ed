"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner@1.7.1_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/sonner@1.7.1_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/sonner@1.7.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/sonner@1.7.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Be),\n/* harmony export */   toast: () => (/* binding */ te),\n/* harmony export */   useSonner: () => (/* binding */ Ce)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar kt = (r)=>{\n    switch(r){\n        case \"success\":\n            return Vt;\n        case \"info\":\n            return Kt;\n        case \"warning\":\n            return Ot;\n        case \"error\":\n            return Jt;\n        default:\n            return null;\n    }\n}, Ut = Array(12).fill(0), Dt = ({ visible: r, className: o })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            o\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": r\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, Ut.map((t, s)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${s}`\n        })))), Vt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), Ot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), Kt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), Jt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n})), Ht = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nvar At = ()=>{\n    let [r, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let t = ()=>{\n            o(document.hidden);\n        };\n        return document.addEventListener(\"visibilitychange\", t), ()=>window.removeEventListener(\"visibilitychange\", t);\n    }, []), r;\n};\n\nvar ft = 1, mt = class {\n    constructor(){\n        this.subscribe = (o)=>(this.subscribers.push(o), ()=>{\n                let t = this.subscribers.indexOf(o);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (o)=>{\n            this.subscribers.forEach((t)=>t(o));\n        };\n        this.addToast = (o)=>{\n            this.publish(o), this.toasts = [\n                ...this.toasts,\n                o\n            ];\n        };\n        this.create = (o)=>{\n            var P;\n            let { message: t, ...s } = o, g = typeof (o == null ? void 0 : o.id) == \"number\" || ((P = o.id) == null ? void 0 : P.length) > 0 ? o.id : ft++, l = this.toasts.find((h)=>h.id === g), E = o.dismissible === void 0 ? !0 : o.dismissible;\n            return l ? this.toasts = this.toasts.map((h)=>h.id === g ? (this.publish({\n                    ...h,\n                    ...o,\n                    id: g,\n                    title: t\n                }), {\n                    ...h,\n                    ...o,\n                    id: g,\n                    dismissible: E,\n                    title: t\n                }) : h) : this.addToast({\n                title: t,\n                ...s,\n                dismissible: E,\n                id: g\n            }), g;\n        };\n        this.dismiss = (o)=>(o || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((s)=>s({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: o,\n                    dismiss: !0\n                })), o);\n        this.message = (o, t)=>this.create({\n                ...t,\n                message: o\n            });\n        this.error = (o, t)=>this.create({\n                ...t,\n                message: o,\n                type: \"error\"\n            });\n        this.success = (o, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: o\n            });\n        this.info = (o, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: o\n            });\n        this.warning = (o, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: o\n            });\n        this.loading = (o, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: o\n            });\n        this.promise = (o, t)=>{\n            if (!t) return;\n            let s;\n            t.loading !== void 0 && (s = this.create({\n                ...t,\n                promise: o,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let g = o instanceof Promise ? o : o(), l = s !== void 0, E, P = g.then(async (c)=>{\n                if (E = [\n                    \"resolve\",\n                    c\n                ], /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(c)) l = !1, this.create({\n                    id: s,\n                    type: \"default\",\n                    message: c\n                });\n                else if (Qt(c) && !c.ok) {\n                    l = !1;\n                    let k = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${c.status}`) : t.error, j = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${c.status}`) : t.description;\n                    this.create({\n                        id: s,\n                        type: \"error\",\n                        message: k,\n                        description: j\n                    });\n                } else if (t.success !== void 0) {\n                    l = !1;\n                    let k = typeof t.success == \"function\" ? await t.success(c) : t.success, j = typeof t.description == \"function\" ? await t.description(c) : t.description;\n                    this.create({\n                        id: s,\n                        type: \"success\",\n                        message: k,\n                        description: j\n                    });\n                }\n            }).catch(async (c)=>{\n                if (E = [\n                    \"reject\",\n                    c\n                ], t.error !== void 0) {\n                    l = !1;\n                    let y = typeof t.error == \"function\" ? await t.error(c) : t.error, k = typeof t.description == \"function\" ? await t.description(c) : t.description;\n                    this.create({\n                        id: s,\n                        type: \"error\",\n                        message: y,\n                        description: k\n                    });\n                }\n            }).finally(()=>{\n                var c;\n                l && (this.dismiss(s), s = void 0), (c = t.finally) == null || c.call(t);\n            }), h = ()=>new Promise((c, y)=>P.then(()=>E[0] === \"reject\" ? y(E[1]) : c(E[1])).catch(y));\n            return typeof s != \"string\" && typeof s != \"number\" ? {\n                unwrap: h\n            } : Object.assign(s, {\n                unwrap: h\n            });\n        };\n        this.custom = (o, t)=>{\n            let s = (t == null ? void 0 : t.id) || ft++;\n            return this.create({\n                jsx: o(s),\n                id: s,\n                ...t\n            }), s;\n        };\n        this.subscribers = [], this.toasts = [];\n    }\n}, T = new mt, Gt = (r, o)=>{\n    let t = (o == null ? void 0 : o.id) || ft++;\n    return T.addToast({\n        title: r,\n        ...o,\n        id: t\n    }), t;\n}, Qt = (r)=>r && typeof r == \"object\" && \"ok\" in r && typeof r.ok == \"boolean\" && \"status\" in r && typeof r.status == \"number\", qt = Gt, Zt = ()=>T.toasts, te = Object.assign(qt, {\n    success: T.success,\n    info: T.info,\n    warning: T.warning,\n    error: T.error,\n    custom: T.custom,\n    message: T.message,\n    promise: T.promise,\n    dismiss: T.dismiss,\n    loading: T.loading\n}, {\n    getHistory: Zt\n});\nfunction pt(r, { insertAt: o } = {}) {\n    if (!r || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], s = document.createElement(\"style\");\n    s.type = \"text/css\", o === \"top\" && t.firstChild ? t.insertBefore(s, t.firstChild) : t.appendChild(s), s.styleSheet ? s.styleSheet.cssText = r : s.appendChild(document.createTextNode(r));\n}\npt(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\"left\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction V(r) {\n    return r.label !== void 0;\n}\nvar ae = 3, ne = \"32px\", Lt = 4e3, se = 356, re = 14, ie = 20, le = 200;\nfunction de(...r) {\n    return r.filter(Boolean).join(\" \");\n}\nvar ce = (r)=>{\n    var xt, vt, wt, Tt, Rt, St, Et, Nt, Pt, Ct, Bt;\n    let { invert: o, toast: t, unstyled: s, interacting: g, setHeights: l, visibleToasts: E, heights: P, index: h, toasts: c, expanded: y, removeToast: k, defaultRichColors: j, closeButton: O, style: st, cancelButtonStyle: i, actionButtonStyle: K, className: J = \"\", descriptionClassName: rt = \"\", duration: _, position: it, gap: lt, loadingIcon: X, expandByDefault: C, classNames: a, icons: N, closeButtonAriaLabel: G = \"Close toast\", pauseWhenPageIsHidden: Q, cn: R } = r, [B, q] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [U, dt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [M, A] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [Z, L] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [Y, tt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [d, u] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [b, w] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), D = react__WEBPACK_IMPORTED_MODULE_0__.useRef(t.duration || _ || Lt), f = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), H = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), et = h === 0, ot = h + 1 <= E, x = t.type, F = t.dismissible !== !1, jt = t.className || \"\", Yt = t.descriptionClassName || \"\", at = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>P.findIndex((n)=>n.toastId === t.id) || 0, [\n        P,\n        t.id\n    ]), Ft = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var n;\n        return (n = t.closeButton) != null ? n : O;\n    }, [\n        t.closeButton,\n        O\n    ]), ue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>t.duration || _ || Lt, [\n        t.duration,\n        _\n    ]), ct = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), $ = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), gt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), nt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [ht, $t] = it.split(\"-\"), bt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>P.reduce((n, m, p)=>p >= at ? n : n + m.height, 0), [\n        P,\n        at\n    ]), yt = At(), Wt = t.invert || o, ut = x === \"loading\";\n    $.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>at * lt + bt, [\n        at,\n        bt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        q(!0);\n    }, []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let n = H.current;\n        if (n) {\n            let m = n.getBoundingClientRect().height;\n            return w(m), l((p)=>[\n                    {\n                        toastId: t.id,\n                        height: m,\n                        position: t.position\n                    },\n                    ...p\n                ]), ()=>l((p)=>p.filter((v)=>v.toastId !== t.id));\n        }\n    }, [\n        l,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        if (!B) return;\n        let n = H.current, m = n.style.height;\n        n.style.height = \"auto\";\n        let p = n.getBoundingClientRect().height;\n        n.style.height = m, w(p), l((v)=>v.find((I)=>I.toastId === t.id) ? v.map((I)=>I.toastId === t.id ? {\n                    ...I,\n                    height: p\n                } : I) : [\n                {\n                    toastId: t.id,\n                    height: p,\n                    position: t.position\n                },\n                ...v\n            ]);\n    }, [\n        B,\n        t.title,\n        t.description,\n        l,\n        t.id\n    ]);\n    let z = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        dt(!0), u($.current), l((n)=>n.filter((m)=>m.toastId !== t.id)), setTimeout(()=>{\n            k(t);\n        }, le);\n    }, [\n        t,\n        k,\n        l,\n        $\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (t.promise && x === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n        let n;\n        return y || g || Q && yt ? (()=>{\n            if (gt.current < ct.current) {\n                let v = new Date().getTime() - ct.current;\n                D.current = D.current - v;\n            }\n            gt.current = new Date().getTime();\n        })() : (()=>{\n            D.current !== 1 / 0 && (ct.current = new Date().getTime(), n = setTimeout(()=>{\n                var v;\n                (v = t.onAutoClose) == null || v.call(t, t), z();\n            }, D.current));\n        })(), ()=>clearTimeout(n);\n    }, [\n        y,\n        g,\n        t,\n        x,\n        Q,\n        yt,\n        z\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        t.delete && z();\n    }, [\n        z,\n        t.delete\n    ]);\n    function _t() {\n        var n, m, p;\n        return N != null && N.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: R(a == null ? void 0 : a.loader, (n = t == null ? void 0 : t.classNames) == null ? void 0 : n.loader, \"sonner-loader\"),\n            \"data-visible\": x === \"loading\"\n        }, N.loading) : X ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: R(a == null ? void 0 : a.loader, (m = t == null ? void 0 : t.classNames) == null ? void 0 : m.loader, \"sonner-loader\"),\n            \"data-visible\": x === \"loading\"\n        }, X) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Dt, {\n            className: R(a == null ? void 0 : a.loader, (p = t == null ? void 0 : t.classNames) == null ? void 0 : p.loader),\n            visible: x === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: H,\n        className: R(J, jt, a == null ? void 0 : a.toast, (xt = t == null ? void 0 : t.classNames) == null ? void 0 : xt.toast, a == null ? void 0 : a.default, a == null ? void 0 : a[x], (vt = t == null ? void 0 : t.classNames) == null ? void 0 : vt[x]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (wt = t.richColors) != null ? wt : j,\n        \"data-styled\": !(t.jsx || t.unstyled || s),\n        \"data-mounted\": B,\n        \"data-promise\": !!t.promise,\n        \"data-swiped\": Y,\n        \"data-removed\": U,\n        \"data-visible\": ot,\n        \"data-y-position\": ht,\n        \"data-x-position\": $t,\n        \"data-index\": h,\n        \"data-front\": et,\n        \"data-swiping\": M,\n        \"data-dismissible\": F,\n        \"data-type\": x,\n        \"data-invert\": Wt,\n        \"data-swipe-out\": Z,\n        \"data-expanded\": !!(y || C && B),\n        style: {\n            \"--index\": h,\n            \"--toasts-before\": h,\n            \"--z-index\": c.length - h,\n            \"--offset\": `${U ? d : $.current}px`,\n            \"--initial-height\": C ? \"auto\" : `${b}px`,\n            ...st,\n            ...t.style\n        },\n        onPointerDown: (n)=>{\n            ut || !F || (f.current = new Date, u($.current), n.target.setPointerCapture(n.pointerId), n.target.tagName !== \"BUTTON\" && (A(!0), nt.current = {\n                x: n.clientX,\n                y: n.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var v, W, I, It;\n            if (Z || !F) return;\n            nt.current = null;\n            let n = Number(((v = H.current) == null ? void 0 : v.style.getPropertyValue(\"--swipe-amount\").replace(\"px\", \"\")) || 0), m = new Date().getTime() - ((W = f.current) == null ? void 0 : W.getTime()), p = Math.abs(n) / m;\n            if (Math.abs(n) >= ie || p > .11) {\n                u($.current), (I = t.onDismiss) == null || I.call(t, t), z(), L(!0), tt(!1);\n                return;\n            }\n            (It = H.current) == null || It.style.setProperty(\"--swipe-amount\", \"0px\"), A(!1);\n        },\n        onPointerMove: (n)=>{\n            var W, I;\n            if (!nt.current || !F) return;\n            let m = n.clientY - nt.current.y, p = ((W = window.getSelection()) == null ? void 0 : W.toString().length) > 0, v = ht === \"top\" ? Math.min(0, m) : Math.max(0, m);\n            Math.abs(v) > 0 && tt(!0), !p && ((I = H.current) == null || I.style.setProperty(\"--swipe-amount\", `${v}px`));\n        }\n    }, Ft && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": G,\n        \"data-disabled\": ut,\n        \"data-close-button\": !0,\n        onClick: ut || !F ? ()=>{} : ()=>{\n            var n;\n            z(), (n = t.onDismiss) == null || n.call(t, t);\n        },\n        className: R(a == null ? void 0 : a.closeButton, (Tt = t == null ? void 0 : t.classNames) == null ? void 0 : Tt.closeButton)\n    }, (Rt = N == null ? void 0 : N.close) != null ? Rt : Ht) : null, t.jsx || /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.title) ? t.jsx ? t.jsx : typeof t.title == \"function\" ? t.title() : t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, x || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: R(a == null ? void 0 : a.icon, (St = t == null ? void 0 : t.classNames) == null ? void 0 : St.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || _t() : null, t.type !== \"loading\" ? t.icon || (N == null ? void 0 : N[x]) || kt(x) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: R(a == null ? void 0 : a.content, (Et = t == null ? void 0 : t.classNames) == null ? void 0 : Et.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: R(a == null ? void 0 : a.title, (Nt = t == null ? void 0 : t.classNames) == null ? void 0 : Nt.title)\n    }, typeof t.title == \"function\" ? t.title() : t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: R(rt, Yt, a == null ? void 0 : a.description, (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt.description)\n    }, typeof t.description == \"function\" ? t.description() : t.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.cancel) ? t.cancel : t.cancel && V(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || i,\n        onClick: (n)=>{\n            var m, p;\n            V(t.cancel) && F && ((p = (m = t.cancel).onClick) == null || p.call(m, n), z());\n        },\n        className: R(a == null ? void 0 : a.cancelButton, (Ct = t == null ? void 0 : t.classNames) == null ? void 0 : Ct.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.action) ? t.action : t.action && V(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || K,\n        onClick: (n)=>{\n            var m, p;\n            V(t.action) && ((p = (m = t.action).onClick) == null || p.call(m, n), !n.defaultPrevented && z());\n        },\n        className: R(a == null ? void 0 : a.actionButton, (Bt = t == null ? void 0 : t.classNames) == null ? void 0 : Bt.actionButton)\n    }, t.action.label) : null));\n};\nfunction zt() {\n    if (true) return \"ltr\";\n    let r = document.documentElement.getAttribute(\"dir\");\n    return r === \"auto\" || !r ? window.getComputedStyle(document.documentElement).direction : r;\n}\nfunction Ce() {\n    let [r, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>T.subscribe((t)=>{\n            o((s)=>{\n                if (\"dismiss\" in t && t.dismiss) return s.filter((l)=>l.id !== t.id);\n                let g = s.findIndex((l)=>l.id === t.id);\n                if (g !== -1) {\n                    let l = [\n                        ...s\n                    ];\n                    return l[g] = {\n                        ...l[g],\n                        ...t\n                    }, l;\n                } else return [\n                    t,\n                    ...s\n                ];\n            });\n        }), []), {\n        toasts: r\n    };\n}\nvar Be = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(o, t) {\n    let { invert: s, position: g = \"bottom-right\", hotkey: l = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: E, closeButton: P, className: h, offset: c, theme: y = \"light\", richColors: k, duration: j, style: O, visibleToasts: st = ae, toastOptions: i, dir: K = zt(), gap: J = re, loadingIcon: rt, icons: _, containerAriaLabel: it = \"Notifications\", pauseWhenPageIsHidden: lt, cn: X = de } = o, [C, a] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), N = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Array.from(new Set([\n            g\n        ].concat(C.filter((d)=>d.position).map((d)=>d.position)))), [\n        C,\n        g\n    ]), [G, Q] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [R, B] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [q, U] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [dt, M] = react__WEBPACK_IMPORTED_MODULE_0__.useState(y !== \"system\" ? y :  false ? 0 : \"light\"), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Z = l.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), L = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Y = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), tt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((d)=>{\n        a((u)=>{\n            var b;\n            return (b = u.find((w)=>w.id === d.id)) != null && b.delete || T.dismiss(d.id), u.filter(({ id: w })=>w !== d.id);\n        });\n    }, []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>T.subscribe((d)=>{\n            if (d.dismiss) {\n                a((u)=>u.map((b)=>b.id === d.id ? {\n                            ...b,\n                            delete: !0\n                        } : b));\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    a((u)=>{\n                        let b = u.findIndex((w)=>w.id === d.id);\n                        return b !== -1 ? [\n                            ...u.slice(0, b),\n                            {\n                                ...u[b],\n                                ...d\n                            },\n                            ...u.slice(b + 1)\n                        ] : [\n                            d,\n                            ...u\n                        ];\n                    });\n                });\n            });\n        }), []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (y !== \"system\") {\n            M(y);\n            return;\n        }\n        if (y === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? M(\"dark\") : M(\"light\")), \"undefined\" == \"undefined\") return;\n        let d = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        try {\n            d.addEventListener(\"change\", ({ matches: u })=>{\n                M(u ? \"dark\" : \"light\");\n            });\n        } catch (u) {\n            d.addListener(({ matches: b })=>{\n                try {\n                    M(b ? \"dark\" : \"light\");\n                } catch (w) {\n                    console.error(w);\n                }\n            });\n        }\n    }, [\n        y\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        C.length <= 1 && B(!1);\n    }, [\n        C\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let d = (u)=>{\n            var w, D;\n            l.every((f)=>u[f] || u.code === f) && (B(!0), (w = A.current) == null || w.focus()), u.code === \"Escape\" && (document.activeElement === A.current || (D = A.current) != null && D.contains(document.activeElement)) && B(!1);\n        };\n        return document.addEventListener(\"keydown\", d), ()=>document.removeEventListener(\"keydown\", d);\n    }, [\n        l\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (A.current) return ()=>{\n            L.current && (L.current.focus({\n                preventScroll: !0\n            }), L.current = null, Y.current = !1);\n        };\n    }, [\n        A.current\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        \"aria-label\": `${it} ${Z}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\"\n    }, N.map((d, u)=>{\n        var D;\n        let [b, w] = d.split(\"-\");\n        return C.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: d,\n            dir: K === \"auto\" ? zt() : K,\n            tabIndex: -1,\n            ref: A,\n            className: h,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": dt,\n            \"data-y-position\": b,\n            \"data-lifted\": R && C.length > 1 && !E,\n            \"data-x-position\": w,\n            style: {\n                \"--front-toast-height\": `${((D = G[0]) == null ? void 0 : D.height) || 0}px`,\n                \"--offset\": typeof c == \"number\" ? `${c}px` : c || ne,\n                \"--width\": `${se}px`,\n                \"--gap\": `${J}px`,\n                ...O\n            },\n            onBlur: (f)=>{\n                Y.current && !f.currentTarget.contains(f.relatedTarget) && (Y.current = !1, L.current && (L.current.focus({\n                    preventScroll: !0\n                }), L.current = null));\n            },\n            onFocus: (f)=>{\n                f.target instanceof HTMLElement && f.target.dataset.dismissible === \"false\" || Y.current || (Y.current = !0, L.current = f.relatedTarget);\n            },\n            onMouseEnter: ()=>B(!0),\n            onMouseMove: ()=>B(!0),\n            onMouseLeave: ()=>{\n                q || B(!1);\n            },\n            onPointerDown: (f)=>{\n                f.target instanceof HTMLElement && f.target.dataset.dismissible === \"false\" || U(!0);\n            },\n            onPointerUp: ()=>U(!1)\n        }, C.filter((f)=>!f.position && u === 0 || f.position === d).map((f, H)=>{\n            var et, ot;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ce, {\n                key: f.id,\n                icons: _,\n                index: H,\n                toast: f,\n                defaultRichColors: k,\n                duration: (et = i == null ? void 0 : i.duration) != null ? et : j,\n                className: i == null ? void 0 : i.className,\n                descriptionClassName: i == null ? void 0 : i.descriptionClassName,\n                invert: s,\n                visibleToasts: st,\n                closeButton: (ot = i == null ? void 0 : i.closeButton) != null ? ot : P,\n                interacting: q,\n                position: d,\n                style: i == null ? void 0 : i.style,\n                unstyled: i == null ? void 0 : i.unstyled,\n                classNames: i == null ? void 0 : i.classNames,\n                cancelButtonStyle: i == null ? void 0 : i.cancelButtonStyle,\n                actionButtonStyle: i == null ? void 0 : i.actionButtonStyle,\n                removeToast: tt,\n                toasts: C.filter((x)=>x.position == f.position),\n                heights: G.filter((x)=>x.position == f.position),\n                setHeights: Q,\n                expandByDefault: E,\n                gap: J,\n                loadingIcon: rt,\n                expanded: R,\n                pauseWhenPageIsHidden: lt,\n                cn: X\n            });\n        })) : null;\n    }));\n});\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/sonner@1.7.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;