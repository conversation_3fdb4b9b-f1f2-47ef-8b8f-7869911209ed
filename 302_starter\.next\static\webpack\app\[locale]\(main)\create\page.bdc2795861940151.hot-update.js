"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx":
/*!*************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/aspect-ratio */ \"(app-pages-browser)/./src/components/ui/aspect-ratio.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst VideoPreviewFrame = (param)=>{\n    let { coverImage, aspectRatio = 16 / 9, placeholder = \"视频预览\" } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleImageError = ()=>{\n        setImageError(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__.AspectRatio, {\n            ratio: aspectRatio,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border bg-muted\",\n                children: coverImage && !imageError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: coverImage,\n                    alt: \"Video preview\",\n                    fill: true,\n                    className: \"rounded-md object-cover\",\n                    onError: handleImageError,\n                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-8 w-8 text-muted-foreground\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-muted-foreground\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoPreviewFrame, \"gLR0P7wgc8ZXiun/rQPANvAzwwQ=\");\n_c = VideoPreviewFrame;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VideoPreviewFrame);\nvar _c;\n$RefreshReg$(_c, \"VideoPreviewFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/index.ts":
/*!************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/index.ts ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigurationPanel: function() { return /* reexport safe */ _ConfigurationPanel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   VideoPreviewFrame: function() { return /* reexport safe */ _VideoPreviewFrame__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _VideoPreviewFrame__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./VideoPreviewFrame */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\");\n/* harmony import */ var _ConfigurationPanel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConfigurationPanel */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx\");\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vKG1haW4pL2NyZWF0ZS9jb21wb25lbnRzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUU7QUFDRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL1tsb2NhbGVdLyhtYWluKS9jcmVhdGUvY29tcG9uZW50cy9pbmRleC50cz8wMGIxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgYXMgVmlkZW9QcmV2aWV3RnJhbWUgfSBmcm9tIFwiLi9WaWRlb1ByZXZpZXdGcmFtZVwiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb25maWd1cmF0aW9uUGFuZWwgfSBmcm9tIFwiLi9Db25maWd1cmF0aW9uUGFuZWxcIjtcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiVmlkZW9QcmV2aWV3RnJhbWUiLCJDb25maWd1cmF0aW9uUGFuZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts":
/*!***************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/hooks/useCreatePage.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreatePage: function() { return /* binding */ useCreatePage; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useCreatePage = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        smallSelectValue: \"\",\n        largeSelectValue: \"\",\n        textContent: \"\",\n        coverImageUrl: undefined\n    });\n    const updateSmallSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                smallSelectValue: value\n            }));\n    };\n    const updateLargeSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                largeSelectValue: value\n            }));\n    };\n    const updateTextContent = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                textContent: value\n            }));\n    };\n    const updateCoverImage = (url)=>{\n        setState((prev)=>({\n                ...prev,\n                coverImageUrl: url\n            }));\n    };\n    return {\n        state,\n        updateSmallSelect,\n        updateLargeSelect,\n        updateTextContent,\n        updateCoverImage\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\n"));

/***/ })

});