"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/(main)/create/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/index.ts\");\n/* harmony import */ var _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hooks/useCreatePage */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\");\n/* harmony import */ var _stores__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores */ \"(app-pages-browser)/./src/stores/index.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n/* harmony import */ var ky__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ky */ \"(app-pages-browser)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/env */ \"(app-pages-browser)/./src/env.ts\");\n/* harmony import */ var _constants_voices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/constants/voices */ \"(app-pages-browser)/./src/constants/voices.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nasync function fetchTTSProviders(apiKey) {\n    if (!apiKey) {\n        return;\n    }\n    try {\n        const response = await ky__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(_env__WEBPACK_IMPORTED_MODULE_7__.env.NEXT_PUBLIC_API_URL, \"/302/tts/provider\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"API调用失败: \".concat(response.status));\n        }\n        const result = await response.json();\n        return result;\n    } catch (err) {\n        const errorText = await err.response.text();\n        const errorData = JSON.parse(errorText);\n        if (errorData.error && errorData.error.err_code) {\n        // toast.error(() => ErrorToast(errorData.error.err_code));\n        } else {\n        // toast.error(\"获取供应商失败\");\n        }\n    }\n}\nconst CreatePage = ()=>{\n    _s();\n    const { state, updateSmallSelect, updateLargeSelect, updateTextContent, updateBackground } = (0,_hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage)();\n    const { apiKey } = _stores__WEBPACK_IMPORTED_MODULE_5__.store.get(_stores__WEBPACK_IMPORTED_MODULE_5__.appConfigAtom);\n    const [voiceStore, setVoiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__.voiceStoreAtom);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (async ()=>{\n            const providerData = await fetchTTSProviders(apiKey);\n            if (!providerData) {\n                return;\n            }\n            const { provider_list } = providerData;\n            // 处理 doubao 数据\n            const doubaoProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"doubao\");\n            if (doubaoProvider) {\n                const doubaoVoiceList = doubaoProvider.req_params_info.voice_list || [];\n                const doubaoVoiceOptions = doubaoVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Doubao children\n                const doubaoVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Doubao\");\n                if (doubaoVoice) {\n                    doubaoVoice.children = doubaoVoiceOptions;\n                }\n            }\n            // 处理 fish 数据\n            const fishProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"fish\");\n            if (fishProvider) {\n                const fishVoiceList = fishProvider.req_params_info.voice_list || [];\n                const fishVoiceOptions = fishVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.name || voice.voice,\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 FishAudio children\n                const fishVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"fish\");\n                if (fishVoice) {\n                    fishVoice.children = fishVoiceOptions;\n                }\n            }\n            // 处理 minimax 数据\n            const minimaxProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"minimaxi\");\n            if (minimaxProvider) {\n                const minimaxVoiceList = minimaxProvider.req_params_info.voice_list || [];\n                const minimaxVoiceOptions = minimaxVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Minimax children\n                const minimaxVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Minimaxi\");\n                if (minimaxVoice) {\n                    minimaxVoice.children = minimaxVoiceOptions;\n                }\n            }\n            // // 处理 dubbingxi 数据\n            // const dubbingxiProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"dubbingx\"\n            // );\n            // if (dubbingxiProvider) {\n            //   const dubbingxiVoiceList =\n            //     dubbingxiProvider.req_params_info.voice_list || [];\n            //   const dubbingxiVoiceOptions: VoiceOption[] = dubbingxiVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: `${voice.name} (${t(voice.gender.toLowerCase())})`,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 dubbingxi children\n            //   const dubbingxiVoice = voices.find((v) => v.key === \"dubbingx\");\n            //   if (dubbingxiVoice) {\n            //     dubbingxiVoice.children = dubbingxiVoiceOptions;\n            //   }\n            // }\n            // // 处理 elevenlabs 数据\n            // const elevenlabsProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"elevenlabs\"\n            // );\n            // if (elevenlabsProvider) {\n            //   const elevenlabsVoiceList =\n            //     elevenlabsProvider.req_params_info.voice_list || [];\n            //   const elevenlabsVoiceOptions: VoiceOption[] = elevenlabsVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: voice.name || voice.voice,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 elevenlabs children\n            //   const elevenlabsVoice = voices.find((v) => v.key === \"elevenlabs\");\n            //   if (elevenlabsVoice) {\n            //     elevenlabsVoice.children = elevenlabsVoiceOptions;\n            //   }\n            // }\n            // 处理 openai 数据\n            const openAiProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"openai\");\n            if (openAiProvider) {\n                const openAiVoiceList = openAiProvider.req_params_info.voice_list || [];\n                // 只保留指定的几个音色\n                const allowedVoices = [\n                    \"alloy\",\n                    \"echo\",\n                    \"fable\",\n                    \"onyx\",\n                    \"nova\",\n                    \"shimmer\"\n                ];\n                const filteredOpenAiVoiceList = openAiVoiceList.filter((voice)=>allowedVoices.includes(voice.voice.toLowerCase()));\n                const openAiVoiceOptions = filteredOpenAiVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.gender ? \"\".concat(voice.name.charAt(0).toUpperCase() + voice.name.slice(1), \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\") : voice.name.charAt(0).toUpperCase() + voice.name.slice(1),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 openai children\n                const openAiVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"OpenAI\");\n                if (openAiVoice) {\n                    openAiVoice.children = openAiVoiceOptions;\n                }\n            }\n            // 如果需要持久化，可以更新到 store\n            setVoiceStore((prev)=>({\n                    ...prev,\n                    voiceList: _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices\n                }));\n        })();\n    }, [\n        apiKey\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"mx-auto w-full max-w-6xl p-6 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full flex-col gap-6 lg:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-2/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.VideoPreviewFrame, {\n                            coverImage: state.coverImageUrl,\n                            placeholder: \"视频封面预览\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-3/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.ConfigurationPanel, {\n                            smallSelectValue: state.smallSelectValue,\n                            largeSelectValue: state.largeSelectValue,\n                            textContent: state.textContent,\n                            onSmallSelectChange: updateSmallSelect,\n                            onLargeSelectChange: updateLargeSelect,\n                            onTextChange: updateTextContent\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePage, \"cADQ2ZJO/vQf/R2Yf8trRuUrxMo=\", false, function() {\n    return [\n        _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage,\n        jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom\n    ];\n});\n_c = CreatePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreatePage);\nvar _c;\n$RefreshReg$(_c, \"CreatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vKG1haW4pL2NyZWF0ZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDeUM7QUFDRztBQUN5QjtBQUNmO0FBQ047QUFDaEI7QUFDNkI7QUFDekM7QUFDUTtBQUM2QjtBQUV6RCxlQUFlYSxrQkFBa0JDLE1BQTBCO0lBQ3pELElBQUksQ0FBQ0EsUUFBUTtRQUNYO0lBQ0Y7SUFFQSxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNTCwwQ0FBRUEsQ0FBQ00sR0FBRyxDQUMzQixHQUEyQixPQUF4QkwscUNBQUdBLENBQUNNLG1CQUFtQixFQUFDLHNCQUMzQjtZQUNFQyxTQUFTO2dCQUFFQyxlQUFlLFVBQWlCLE9BQVBMO1lBQVM7UUFDL0M7UUFHRixJQUFJLENBQUNDLFNBQVNLLEVBQUUsRUFBRTtZQUNoQixNQUFNLElBQUlDLE1BQU0sWUFBNEIsT0FBaEJOLFNBQVNPLE1BQU07UUFDN0M7UUFFQSxNQUFNQyxTQUFTLE1BQU1SLFNBQVNTLElBQUk7UUFDbEMsT0FBT0Q7SUFDVCxFQUFFLE9BQU9FLEtBQVU7UUFDakIsTUFBTUMsWUFBWSxNQUFNRCxJQUFJVixRQUFRLENBQUNZLElBQUk7UUFDekMsTUFBTUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDSjtRQUM3QixJQUFJRSxVQUFVRyxLQUFLLElBQUlILFVBQVVHLEtBQUssQ0FBQ0MsUUFBUSxFQUFFO1FBQy9DLDJEQUEyRDtRQUM3RCxPQUFPO1FBQ0wsMEJBQTBCO1FBQzVCO0lBQ0Y7QUFDRjtBQUVBLE1BQU1DLGFBQWE7O0lBQ2pCLE1BQU0sRUFDSkMsS0FBSyxFQUNMQyxpQkFBaUIsRUFDakJDLGlCQUFpQixFQUNqQkMsaUJBQWlCLEVBQ2pCQyxnQkFBZ0IsRUFDakIsR0FBR2pDLG1FQUFhQTtJQUVqQixNQUFNLEVBQUVTLE1BQU0sRUFBRSxHQUFHUCwwQ0FBS0EsQ0FBQ1MsR0FBRyxDQUFDVixrREFBYUE7SUFDMUMsTUFBTSxDQUFDaUMsWUFBWUMsY0FBYyxHQUFHaEMsK0NBQU9BLENBQUNDLHNFQUFjQTtJQUUxRFIsZ0RBQVNBLENBQUM7UUFDUDtZQUNDLE1BQU13QyxlQUFlLE1BQU01QixrQkFBa0JDO1lBRTdDLElBQUksQ0FBQzJCLGNBQWM7Z0JBQ2pCO1lBQ0Y7WUFFQSxNQUFNLEVBQUVDLGFBQWEsRUFBRSxHQUFHRDtZQUUxQixlQUFlO1lBQ2YsTUFBTUUsaUJBQWlCRCxjQUFjRSxJQUFJLENBQ3ZDLENBQUNDLElBQU1BLEVBQUVDLFFBQVEsQ0FBQ0MsV0FBVyxPQUFPO1lBRXRDLElBQUlKLGdCQUFnQjtnQkFDbEIsTUFBTUssa0JBQWtCTCxlQUFlTSxlQUFlLENBQUNDLFVBQVUsSUFBSSxFQUFFO2dCQUN2RSxNQUFNQyxxQkFBcUJILGdCQUFnQkksR0FBRyxDQUFDLENBQUNDLFFBQVc7d0JBQ3pEQyxLQUFLRCxNQUFNQSxLQUFLO3dCQUNoQkUsT0FBTyxHQUNMRixPQURRQSxNQUFNRyxJQUFJLEVBQUMsS0FFcEIsT0FEQ0gsTUFBTUksTUFBTSxHQUFHLElBQThDLE9BQTFDQyxFQUFFLFVBQXFDLE9BQTNCTCxNQUFNSSxNQUFNLENBQUNWLFdBQVcsTUFBTSxPQUFLO3dCQUVwRVksT0FBT04sTUFBTUEsS0FBSzt3QkFDbEJPLFlBQVlQO29CQUNkO2dCQUVBLCtCQUErQjtnQkFDL0IsTUFBTVEsY0FBY2pELHFEQUFNQSxDQUFDZ0MsSUFBSSxDQUFDLENBQUNrQixJQUFNQSxFQUFFUixHQUFHLEtBQUs7Z0JBQ2pELElBQUlPLGFBQWE7b0JBQ2ZBLFlBQVlFLFFBQVEsR0FBR1o7Z0JBQ3pCO1lBQ0Y7WUFFQSxhQUFhO1lBQ2IsTUFBTWEsZUFBZXRCLGNBQWNFLElBQUksQ0FDckMsQ0FBQ0MsSUFBTUEsRUFBRUMsUUFBUSxDQUFDQyxXQUFXLE9BQU87WUFFdEMsSUFBSWlCLGNBQWM7Z0JBQ2hCLE1BQU1DLGdCQUFnQkQsYUFBYWYsZUFBZSxDQUFDQyxVQUFVLElBQUksRUFBRTtnQkFDbkUsTUFBTWdCLG1CQUFtQkQsY0FBY2IsR0FBRyxDQUFDLENBQUNDLFFBQVc7d0JBQ3JEQyxLQUFLRCxNQUFNQSxLQUFLO3dCQUNoQkUsT0FBT0YsTUFBTUcsSUFBSSxJQUFJSCxNQUFNQSxLQUFLO3dCQUNoQ00sT0FBT04sTUFBTUEsS0FBSzt3QkFDbEJPLFlBQVlQO29CQUNkO2dCQUVBLGtDQUFrQztnQkFDbEMsTUFBTWMsWUFBWXZELHFEQUFNQSxDQUFDZ0MsSUFBSSxDQUFDLENBQUNrQixJQUFNQSxFQUFFUixHQUFHLEtBQUs7Z0JBQy9DLElBQUlhLFdBQVc7b0JBQ2JBLFVBQVVKLFFBQVEsR0FBR0c7Z0JBQ3ZCO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsTUFBTUUsa0JBQWtCMUIsY0FBY0UsSUFBSSxDQUN4QyxDQUFDQyxJQUFNQSxFQUFFQyxRQUFRLENBQUNDLFdBQVcsT0FBTztZQUV0QyxJQUFJcUIsaUJBQWlCO2dCQUNuQixNQUFNQyxtQkFDSkQsZ0JBQWdCbkIsZUFBZSxDQUFDQyxVQUFVLElBQUksRUFBRTtnQkFDbEQsTUFBTW9CLHNCQUFxQ0QsaUJBQWlCakIsR0FBRyxDQUM3RCxDQUFDQyxRQUFXO3dCQUNWQyxLQUFLRCxNQUFNQSxLQUFLO3dCQUNoQkUsT0FBTyxHQUNMRixPQURRQSxNQUFNRyxJQUFJLEVBQUMsS0FJcEIsT0FIQ0gsTUFBTUksTUFBTSxHQUNSLElBQThDLE9BQTFDQyxFQUFFLFVBQXFDLE9BQTNCTCxNQUFNSSxNQUFNLENBQUNWLFdBQVcsTUFBTSxPQUM5Qzt3QkFFTlksT0FBT04sTUFBTUEsS0FBSzt3QkFDbEJPLFlBQVlQO29CQUNkO2dCQUdGLGdDQUFnQztnQkFDaEMsTUFBTWtCLGVBQWUzRCxxREFBTUEsQ0FBQ2dDLElBQUksQ0FBQyxDQUFDa0IsSUFBTUEsRUFBRVIsR0FBRyxLQUFLO2dCQUNsRCxJQUFJaUIsY0FBYztvQkFDaEJBLGFBQWFSLFFBQVEsR0FBR087Z0JBQzFCO1lBQ0Y7WUFFQSxxQkFBcUI7WUFDckIsZ0RBQWdEO1lBQ2hELG1EQUFtRDtZQUNuRCxLQUFLO1lBQ0wsMkJBQTJCO1lBQzNCLCtCQUErQjtZQUMvQiwwREFBMEQ7WUFDMUQseUVBQXlFO1lBQ3pFLG9CQUFvQjtZQUNwQiwwQkFBMEI7WUFDMUIsbUVBQW1FO1lBQ25FLDRCQUE0QjtZQUM1QiwyQkFBMkI7WUFDM0IsU0FBUztZQUNULE9BQU87WUFFUCx1Q0FBdUM7WUFDdkMscUVBQXFFO1lBQ3JFLDBCQUEwQjtZQUMxQix1REFBdUQ7WUFDdkQsTUFBTTtZQUNOLElBQUk7WUFFSixzQkFBc0I7WUFDdEIsaURBQWlEO1lBQ2pELHFEQUFxRDtZQUNyRCxLQUFLO1lBQ0wsNEJBQTRCO1lBQzVCLGdDQUFnQztZQUNoQywyREFBMkQ7WUFDM0QsMkVBQTJFO1lBQzNFLG9CQUFvQjtZQUNwQiwwQkFBMEI7WUFDMUIsMENBQTBDO1lBQzFDLDRCQUE0QjtZQUM1QiwyQkFBMkI7WUFDM0IsU0FBUztZQUNULE9BQU87WUFFUCx3Q0FBd0M7WUFDeEMsd0VBQXdFO1lBQ3hFLDJCQUEyQjtZQUMzQix5REFBeUQ7WUFDekQsTUFBTTtZQUNOLElBQUk7WUFFSixlQUFlO1lBQ2YsTUFBTUUsaUJBQWlCOUIsY0FBY0UsSUFBSSxDQUN2QyxDQUFDQyxJQUFNQSxFQUFFQyxRQUFRLENBQUNDLFdBQVcsT0FBTztZQUV0QyxJQUFJeUIsZ0JBQWdCO2dCQUNsQixNQUFNQyxrQkFBa0JELGVBQWV2QixlQUFlLENBQUNDLFVBQVUsSUFBSSxFQUFFO2dCQUN2RSxhQUFhO2dCQUNiLE1BQU13QixnQkFBZ0I7b0JBQ3BCO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO2lCQUNEO2dCQUNELE1BQU1DLDBCQUEwQkYsZ0JBQWdCRyxNQUFNLENBQUMsQ0FBQ3ZCLFFBQ3REcUIsY0FBY0csUUFBUSxDQUFDeEIsTUFBTUEsS0FBSyxDQUFDTixXQUFXO2dCQUdoRCxNQUFNK0IscUJBQXFCSCx3QkFBd0J2QixHQUFHLENBQUMsQ0FBQ0MsUUFBVzt3QkFDakVDLEtBQUtELE1BQU1BLEtBQUs7d0JBQ2hCRSxPQUFPRixNQUFNSSxNQUFNLEdBQ2YsR0FDRUosT0FEQ0EsTUFBTUcsSUFBSSxDQUFDdUIsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBSzNCLE1BQU1HLElBQUksQ0FBQ3lCLEtBQUssQ0FBQyxJQUFHLEtBSTNELE9BSEM1QixNQUFNSSxNQUFNLEdBQ1IsSUFBOEMsT0FBMUNDLEVBQUUsVUFBcUMsT0FBM0JMLE1BQU1JLE1BQU0sQ0FBQ1YsV0FBVyxNQUFNLE9BQzlDLE1BRU5NLE1BQU1HLElBQUksQ0FBQ3VCLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUszQixNQUFNRyxJQUFJLENBQUN5QixLQUFLLENBQUM7d0JBQzFEdEIsT0FBT04sTUFBTUEsS0FBSzt3QkFDbEJPLFlBQVlQO29CQUNkO2dCQUVBLCtCQUErQjtnQkFDL0IsTUFBTTZCLGNBQWN0RSxxREFBTUEsQ0FBQ2dDLElBQUksQ0FBQyxDQUFDa0IsSUFBTUEsRUFBRVIsR0FBRyxLQUFLO2dCQUNqRCxJQUFJNEIsYUFBYTtvQkFDZkEsWUFBWW5CLFFBQVEsR0FBR2U7Z0JBQ3pCO1lBQ0Y7WUFFQSxzQkFBc0I7WUFDdEJ0QyxjQUFjLENBQUMyQyxPQUFVO29CQUFFLEdBQUdBLElBQUk7b0JBQUVDLFdBQVd4RSxxREFBTUE7Z0JBQUM7UUFDeEQ7SUFDRixHQUFHO1FBQUNFO0tBQU87SUFFWCxxQkFDRSw4REFBQ3VFO1FBQUlDLFdBQVU7a0JBRWIsNEVBQUNwRixxREFBSUE7WUFBQ29GLFdBQVU7c0JBRWQsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNuRiwwREFBaUJBOzRCQUNoQm9GLFlBQVlyRCxNQUFNc0QsYUFBYTs0QkFDL0JDLGFBQVk7Ozs7Ozs7Ozs7O2tDQUtoQiw4REFBQ0o7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNsRiwyREFBa0JBOzRCQUNqQnNGLGtCQUFrQnhELE1BQU13RCxnQkFBZ0I7NEJBQ3hDQyxrQkFBa0J6RCxNQUFNeUQsZ0JBQWdCOzRCQUN4Q0MsYUFBYTFELE1BQU0wRCxXQUFXOzRCQUM5QkMscUJBQXFCMUQ7NEJBQ3JCMkQscUJBQXFCMUQ7NEJBQ3JCMkQsY0FBYzFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPNUI7R0FsTk1KOztRQU9BNUIsK0RBQWFBO1FBR21CRywyQ0FBT0E7OztLQVZ2Q3lCO0FBb05OLCtEQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvW2xvY2FsZV0vKG1haW4pL2NyZWF0ZS9wYWdlLnRzeD8wNmRkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQ2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xuaW1wb3J0IHsgVmlkZW9QcmV2aWV3RnJhbWUsIENvbmZpZ3VyYXRpb25QYW5lbCB9IGZyb20gXCIuL2NvbXBvbmVudHNcIjtcbmltcG9ydCB7IHVzZUNyZWF0ZVBhZ2UgfSBmcm9tIFwiLi9ob29rcy91c2VDcmVhdGVQYWdlXCI7XG5pbXBvcnQgeyBhcHBDb25maWdBdG9tLCBzdG9yZSB9IGZyb20gXCJAL3N0b3Jlc1wiO1xuaW1wb3J0IHsgdXNlQXRvbSB9IGZyb20gXCJqb3RhaVwiO1xuaW1wb3J0IHsgdm9pY2VTdG9yZUF0b20gfSBmcm9tIFwiQC9zdG9yZXMvc2xpY2VzL3ZvaWNlX3N0b3JlXCI7XG5pbXBvcnQga3kgZnJvbSBcImt5XCI7XG5pbXBvcnQgeyBlbnYgfSBmcm9tIFwiQC9lbnZcIjtcbmltcG9ydCB7IHZvaWNlcywgVm9pY2VPcHRpb24gfSBmcm9tIFwiQC9jb25zdGFudHMvdm9pY2VzXCI7XG5cbmFzeW5jIGZ1bmN0aW9uIGZldGNoVFRTUHJvdmlkZXJzKGFwaUtleTogc3RyaW5nIHwgdW5kZWZpbmVkKSB7XG4gIGlmICghYXBpS2V5KSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGt5LmdldChcbiAgICAgIGAke2Vudi5ORVhUX1BVQkxJQ19BUElfVVJMfS8zMDIvdHRzL3Byb3ZpZGVyYCxcbiAgICAgIHtcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YXBpS2V5fWAgfSxcbiAgICAgIH1cbiAgICApO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBBUEnosIPnlKjlpLHotKU6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IGVyci5yZXNwb25zZS50ZXh0KCk7XG4gICAgY29uc3QgZXJyb3JEYXRhID0gSlNPTi5wYXJzZShlcnJvclRleHQpO1xuICAgIGlmIChlcnJvckRhdGEuZXJyb3IgJiYgZXJyb3JEYXRhLmVycm9yLmVycl9jb2RlKSB7XG4gICAgICAvLyB0b2FzdC5lcnJvcigoKSA9PiBFcnJvclRvYXN0KGVycm9yRGF0YS5lcnJvci5lcnJfY29kZSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyB0b2FzdC5lcnJvcihcIuiOt+WPluS+m+W6lOWVhuWksei0pVwiKTtcbiAgICB9XG4gIH1cbn1cblxuY29uc3QgQ3JlYXRlUGFnZSA9ICgpID0+IHtcbiAgY29uc3Qge1xuICAgIHN0YXRlLFxuICAgIHVwZGF0ZVNtYWxsU2VsZWN0LFxuICAgIHVwZGF0ZUxhcmdlU2VsZWN0LFxuICAgIHVwZGF0ZVRleHRDb250ZW50LFxuICAgIHVwZGF0ZUJhY2tncm91bmQsXG4gIH0gPSB1c2VDcmVhdGVQYWdlKCk7XG5cbiAgY29uc3QgeyBhcGlLZXkgfSA9IHN0b3JlLmdldChhcHBDb25maWdBdG9tKTtcbiAgY29uc3QgW3ZvaWNlU3RvcmUsIHNldFZvaWNlU3RvcmVdID0gdXNlQXRvbSh2b2ljZVN0b3JlQXRvbSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAoYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcHJvdmlkZXJEYXRhID0gYXdhaXQgZmV0Y2hUVFNQcm92aWRlcnMoYXBpS2V5KTtcblxuICAgICAgaWYgKCFwcm92aWRlckRhdGEpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7IHByb3ZpZGVyX2xpc3QgfSA9IHByb3ZpZGVyRGF0YTtcblxuICAgICAgLy8g5aSE55CGIGRvdWJhbyDmlbDmja5cbiAgICAgIGNvbnN0IGRvdWJhb1Byb3ZpZGVyID0gcHJvdmlkZXJfbGlzdC5maW5kKFxuICAgICAgICAocCkgPT4gcC5wcm92aWRlci50b0xvd2VyQ2FzZSgpID09PSBcImRvdWJhb1wiXG4gICAgICApO1xuICAgICAgaWYgKGRvdWJhb1Byb3ZpZGVyKSB7XG4gICAgICAgIGNvbnN0IGRvdWJhb1ZvaWNlTGlzdCA9IGRvdWJhb1Byb3ZpZGVyLnJlcV9wYXJhbXNfaW5mby52b2ljZV9saXN0IHx8IFtdO1xuICAgICAgICBjb25zdCBkb3ViYW9Wb2ljZU9wdGlvbnMgPSBkb3ViYW9Wb2ljZUxpc3QubWFwKCh2b2ljZSkgPT4gKHtcbiAgICAgICAgICBrZXk6IHZvaWNlLnZvaWNlLFxuICAgICAgICAgIGxhYmVsOiBgJHt2b2ljZS5uYW1lfSAke1xuICAgICAgICAgICAgdm9pY2UuZ2VuZGVyID8gYCgke3QoYGNvbW1vbi4ke3ZvaWNlLmdlbmRlci50b0xvd2VyQ2FzZSgpfWApfSlgIDogXCJcIlxuICAgICAgICAgIH1gLFxuICAgICAgICAgIHZhbHVlOiB2b2ljZS52b2ljZSxcbiAgICAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIERvdWJhbyBjaGlsZHJlblxuICAgICAgICBjb25zdCBkb3ViYW9Wb2ljZSA9IHZvaWNlcy5maW5kKCh2KSA9PiB2LmtleSA9PT0gXCJEb3ViYW9cIik7XG4gICAgICAgIGlmIChkb3ViYW9Wb2ljZSkge1xuICAgICAgICAgIGRvdWJhb1ZvaWNlLmNoaWxkcmVuID0gZG91YmFvVm9pY2VPcHRpb25zO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWkhOeQhiBmaXNoIOaVsOaNrlxuICAgICAgY29uc3QgZmlzaFByb3ZpZGVyID0gcHJvdmlkZXJfbGlzdC5maW5kKFxuICAgICAgICAocCkgPT4gcC5wcm92aWRlci50b0xvd2VyQ2FzZSgpID09PSBcImZpc2hcIlxuICAgICAgKTtcbiAgICAgIGlmIChmaXNoUHJvdmlkZXIpIHtcbiAgICAgICAgY29uc3QgZmlzaFZvaWNlTGlzdCA9IGZpc2hQcm92aWRlci5yZXFfcGFyYW1zX2luZm8udm9pY2VfbGlzdCB8fCBbXTtcbiAgICAgICAgY29uc3QgZmlzaFZvaWNlT3B0aW9ucyA9IGZpc2hWb2ljZUxpc3QubWFwKCh2b2ljZSkgPT4gKHtcbiAgICAgICAgICBrZXk6IHZvaWNlLnZvaWNlLFxuICAgICAgICAgIGxhYmVsOiB2b2ljZS5uYW1lIHx8IHZvaWNlLnZvaWNlLFxuICAgICAgICAgIHZhbHVlOiB2b2ljZS52b2ljZSxcbiAgICAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIEZpc2hBdWRpbyBjaGlsZHJlblxuICAgICAgICBjb25zdCBmaXNoVm9pY2UgPSB2b2ljZXMuZmluZCgodikgPT4gdi5rZXkgPT09IFwiZmlzaFwiKTtcbiAgICAgICAgaWYgKGZpc2hWb2ljZSkge1xuICAgICAgICAgIGZpc2hWb2ljZS5jaGlsZHJlbiA9IGZpc2hWb2ljZU9wdGlvbnM7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5aSE55CGIG1pbmltYXgg5pWw5o2uXG4gICAgICBjb25zdCBtaW5pbWF4UHJvdmlkZXIgPSBwcm92aWRlcl9saXN0LmZpbmQoXG4gICAgICAgIChwKSA9PiBwLnByb3ZpZGVyLnRvTG93ZXJDYXNlKCkgPT09IFwibWluaW1heGlcIlxuICAgICAgKTtcbiAgICAgIGlmIChtaW5pbWF4UHJvdmlkZXIpIHtcbiAgICAgICAgY29uc3QgbWluaW1heFZvaWNlTGlzdCA9XG4gICAgICAgICAgbWluaW1heFByb3ZpZGVyLnJlcV9wYXJhbXNfaW5mby52b2ljZV9saXN0IHx8IFtdO1xuICAgICAgICBjb25zdCBtaW5pbWF4Vm9pY2VPcHRpb25zOiBWb2ljZU9wdGlvbltdID0gbWluaW1heFZvaWNlTGlzdC5tYXAoXG4gICAgICAgICAgKHZvaWNlKSA9PiAoe1xuICAgICAgICAgICAga2V5OiB2b2ljZS52b2ljZSxcbiAgICAgICAgICAgIGxhYmVsOiBgJHt2b2ljZS5uYW1lfSAke1xuICAgICAgICAgICAgICB2b2ljZS5nZW5kZXJcbiAgICAgICAgICAgICAgICA/IGAoJHt0KGBjb21tb24uJHt2b2ljZS5nZW5kZXIudG9Mb3dlckNhc2UoKX1gKX0pYFxuICAgICAgICAgICAgICAgIDogXCJcIlxuICAgICAgICAgICAgfWAsXG4gICAgICAgICAgICB2YWx1ZTogdm9pY2Uudm9pY2UsXG4gICAgICAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgICAgICB9KVxuICAgICAgICApO1xuXG4gICAgICAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIE1pbmltYXggY2hpbGRyZW5cbiAgICAgICAgY29uc3QgbWluaW1heFZvaWNlID0gdm9pY2VzLmZpbmQoKHYpID0+IHYua2V5ID09PSBcIk1pbmltYXhpXCIpO1xuICAgICAgICBpZiAobWluaW1heFZvaWNlKSB7XG4gICAgICAgICAgbWluaW1heFZvaWNlLmNoaWxkcmVuID0gbWluaW1heFZvaWNlT3B0aW9ucztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyAvLyDlpITnkIYgZHViYmluZ3hpIOaVsOaNrlxuICAgICAgLy8gY29uc3QgZHViYmluZ3hpUHJvdmlkZXIgPSBwcm92aWRlcl9saXN0LmZpbmQoXG4gICAgICAvLyAgIChwKSA9PiBwLnByb3ZpZGVyLnRvTG93ZXJDYXNlKCkgPT09IFwiZHViYmluZ3hcIlxuICAgICAgLy8gKTtcbiAgICAgIC8vIGlmIChkdWJiaW5neGlQcm92aWRlcikge1xuICAgICAgLy8gICBjb25zdCBkdWJiaW5neGlWb2ljZUxpc3QgPVxuICAgICAgLy8gICAgIGR1YmJpbmd4aVByb3ZpZGVyLnJlcV9wYXJhbXNfaW5mby52b2ljZV9saXN0IHx8IFtdO1xuICAgICAgLy8gICBjb25zdCBkdWJiaW5neGlWb2ljZU9wdGlvbnM6IFZvaWNlT3B0aW9uW10gPSBkdWJiaW5neGlWb2ljZUxpc3QubWFwKFxuICAgICAgLy8gICAgICh2b2ljZSkgPT4gKHtcbiAgICAgIC8vICAgICAgIGtleTogdm9pY2Uudm9pY2UsXG4gICAgICAvLyAgICAgICBsYWJlbDogYCR7dm9pY2UubmFtZX0gKCR7dCh2b2ljZS5nZW5kZXIudG9Mb3dlckNhc2UoKSl9KWAsXG4gICAgICAvLyAgICAgICB2YWx1ZTogdm9pY2Uudm9pY2UsXG4gICAgICAvLyAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgIC8vICAgICB9KVxuICAgICAgLy8gICApO1xuXG4gICAgICAvLyAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIGR1YmJpbmd4aSBjaGlsZHJlblxuICAgICAgLy8gICBjb25zdCBkdWJiaW5neGlWb2ljZSA9IHZvaWNlcy5maW5kKCh2KSA9PiB2LmtleSA9PT0gXCJkdWJiaW5neFwiKTtcbiAgICAgIC8vICAgaWYgKGR1YmJpbmd4aVZvaWNlKSB7XG4gICAgICAvLyAgICAgZHViYmluZ3hpVm9pY2UuY2hpbGRyZW4gPSBkdWJiaW5neGlWb2ljZU9wdGlvbnM7XG4gICAgICAvLyAgIH1cbiAgICAgIC8vIH1cblxuICAgICAgLy8gLy8g5aSE55CGIGVsZXZlbmxhYnMg5pWw5o2uXG4gICAgICAvLyBjb25zdCBlbGV2ZW5sYWJzUHJvdmlkZXIgPSBwcm92aWRlcl9saXN0LmZpbmQoXG4gICAgICAvLyAgIChwKSA9PiBwLnByb3ZpZGVyLnRvTG93ZXJDYXNlKCkgPT09IFwiZWxldmVubGFic1wiXG4gICAgICAvLyApO1xuICAgICAgLy8gaWYgKGVsZXZlbmxhYnNQcm92aWRlcikge1xuICAgICAgLy8gICBjb25zdCBlbGV2ZW5sYWJzVm9pY2VMaXN0ID1cbiAgICAgIC8vICAgICBlbGV2ZW5sYWJzUHJvdmlkZXIucmVxX3BhcmFtc19pbmZvLnZvaWNlX2xpc3QgfHwgW107XG4gICAgICAvLyAgIGNvbnN0IGVsZXZlbmxhYnNWb2ljZU9wdGlvbnM6IFZvaWNlT3B0aW9uW10gPSBlbGV2ZW5sYWJzVm9pY2VMaXN0Lm1hcChcbiAgICAgIC8vICAgICAodm9pY2UpID0+ICh7XG4gICAgICAvLyAgICAgICBrZXk6IHZvaWNlLnZvaWNlLFxuICAgICAgLy8gICAgICAgbGFiZWw6IHZvaWNlLm5hbWUgfHwgdm9pY2Uudm9pY2UsXG4gICAgICAvLyAgICAgICB2YWx1ZTogdm9pY2Uudm9pY2UsXG4gICAgICAvLyAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgIC8vICAgICB9KVxuICAgICAgLy8gICApO1xuXG4gICAgICAvLyAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIGVsZXZlbmxhYnMgY2hpbGRyZW5cbiAgICAgIC8vICAgY29uc3QgZWxldmVubGFic1ZvaWNlID0gdm9pY2VzLmZpbmQoKHYpID0+IHYua2V5ID09PSBcImVsZXZlbmxhYnNcIik7XG4gICAgICAvLyAgIGlmIChlbGV2ZW5sYWJzVm9pY2UpIHtcbiAgICAgIC8vICAgICBlbGV2ZW5sYWJzVm9pY2UuY2hpbGRyZW4gPSBlbGV2ZW5sYWJzVm9pY2VPcHRpb25zO1xuICAgICAgLy8gICB9XG4gICAgICAvLyB9XG5cbiAgICAgIC8vIOWkhOeQhiBvcGVuYWkg5pWw5o2uXG4gICAgICBjb25zdCBvcGVuQWlQcm92aWRlciA9IHByb3ZpZGVyX2xpc3QuZmluZChcbiAgICAgICAgKHApID0+IHAucHJvdmlkZXIudG9Mb3dlckNhc2UoKSA9PT0gXCJvcGVuYWlcIlxuICAgICAgKTtcbiAgICAgIGlmIChvcGVuQWlQcm92aWRlcikge1xuICAgICAgICBjb25zdCBvcGVuQWlWb2ljZUxpc3QgPSBvcGVuQWlQcm92aWRlci5yZXFfcGFyYW1zX2luZm8udm9pY2VfbGlzdCB8fCBbXTtcbiAgICAgICAgLy8g5Y+q5L+d55WZ5oyH5a6a55qE5Yeg5Liq6Z+z6ImyXG4gICAgICAgIGNvbnN0IGFsbG93ZWRWb2ljZXMgPSBbXG4gICAgICAgICAgXCJhbGxveVwiLFxuICAgICAgICAgIFwiZWNob1wiLFxuICAgICAgICAgIFwiZmFibGVcIixcbiAgICAgICAgICBcIm9ueXhcIixcbiAgICAgICAgICBcIm5vdmFcIixcbiAgICAgICAgICBcInNoaW1tZXJcIixcbiAgICAgICAgXTtcbiAgICAgICAgY29uc3QgZmlsdGVyZWRPcGVuQWlWb2ljZUxpc3QgPSBvcGVuQWlWb2ljZUxpc3QuZmlsdGVyKCh2b2ljZSkgPT5cbiAgICAgICAgICBhbGxvd2VkVm9pY2VzLmluY2x1ZGVzKHZvaWNlLnZvaWNlLnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc3Qgb3BlbkFpVm9pY2VPcHRpb25zID0gZmlsdGVyZWRPcGVuQWlWb2ljZUxpc3QubWFwKCh2b2ljZSkgPT4gKHtcbiAgICAgICAgICBrZXk6IHZvaWNlLnZvaWNlLFxuICAgICAgICAgIGxhYmVsOiB2b2ljZS5nZW5kZXJcbiAgICAgICAgICAgID8gYCR7dm9pY2UubmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHZvaWNlLm5hbWUuc2xpY2UoMSl9ICR7XG4gICAgICAgICAgICAgICAgdm9pY2UuZ2VuZGVyXG4gICAgICAgICAgICAgICAgICA/IGAoJHt0KGBjb21tb24uJHt2b2ljZS5nZW5kZXIudG9Mb3dlckNhc2UoKX1gKX0pYFxuICAgICAgICAgICAgICAgICAgOiBcIlwiXG4gICAgICAgICAgICAgIH1gXG4gICAgICAgICAgICA6IHZvaWNlLm5hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyB2b2ljZS5uYW1lLnNsaWNlKDEpLFxuICAgICAgICAgIHZhbHVlOiB2b2ljZS52b2ljZSxcbiAgICAgICAgICBvcmlnaW5EYXRhOiB2b2ljZSxcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIOabtOaWsCB2b2ljZXMg5Lit55qEIG9wZW5haSBjaGlsZHJlblxuICAgICAgICBjb25zdCBvcGVuQWlWb2ljZSA9IHZvaWNlcy5maW5kKCh2KSA9PiB2LmtleSA9PT0gXCJPcGVuQUlcIik7XG4gICAgICAgIGlmIChvcGVuQWlWb2ljZSkge1xuICAgICAgICAgIG9wZW5BaVZvaWNlLmNoaWxkcmVuID0gb3BlbkFpVm9pY2VPcHRpb25zO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOmcgOimgeaMgeS5heWMlu+8jOWPr+S7peabtOaWsOWIsCBzdG9yZVxuICAgICAgc2V0Vm9pY2VTdG9yZSgocHJldikgPT4gKHsgLi4ucHJldiwgdm9pY2VMaXN0OiB2b2ljZXMgfSkpO1xuICAgIH0pKCk7XG4gIH0sIFthcGlLZXldKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBiZy1iYWNrZ3JvdW5kIHAtNFwiPlxuICAgICAgey8qIOS4u+WuueWZqCAtIOWxheS4reaYvuekuiAqL31cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm14LWF1dG8gdy1mdWxsIG1heC13LTZ4bCBwLTYgc2hhZG93LXNtXCI+XG4gICAgICAgIHsvKiDlt6blj7PliIbmoI/luIPlsYAgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLWZ1bGwgZmxleC1jb2wgZ2FwLTYgbGc6ZmxleC1yb3dcIj5cbiAgICAgICAgICB7Lyog5bem5L6n5Yy65Z+fIC0g6KeG6aKR6aKE6KeIICg0MCXlrr3luqYpICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGxnOnctMi81XCI+XG4gICAgICAgICAgICA8VmlkZW9QcmV2aWV3RnJhbWVcbiAgICAgICAgICAgICAgY292ZXJJbWFnZT17c3RhdGUuY292ZXJJbWFnZVVybH1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLop4bpopHlsIHpnaLpooTop4hcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlj7PkvqfljLrln58gLSDphY3nva7pgInpobkgKDYwJeWuveW6pikgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbGc6dy0zLzVcIj5cbiAgICAgICAgICAgIDxDb25maWd1cmF0aW9uUGFuZWxcbiAgICAgICAgICAgICAgc21hbGxTZWxlY3RWYWx1ZT17c3RhdGUuc21hbGxTZWxlY3RWYWx1ZX1cbiAgICAgICAgICAgICAgbGFyZ2VTZWxlY3RWYWx1ZT17c3RhdGUubGFyZ2VTZWxlY3RWYWx1ZX1cbiAgICAgICAgICAgICAgdGV4dENvbnRlbnQ9e3N0YXRlLnRleHRDb250ZW50fVxuICAgICAgICAgICAgICBvblNtYWxsU2VsZWN0Q2hhbmdlPXt1cGRhdGVTbWFsbFNlbGVjdH1cbiAgICAgICAgICAgICAgb25MYXJnZVNlbGVjdENoYW5nZT17dXBkYXRlTGFyZ2VTZWxlY3R9XG4gICAgICAgICAgICAgIG9uVGV4dENoYW5nZT17dXBkYXRlVGV4dENvbnRlbnR9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZD5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENyZWF0ZVBhZ2U7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJDYXJkIiwiVmlkZW9QcmV2aWV3RnJhbWUiLCJDb25maWd1cmF0aW9uUGFuZWwiLCJ1c2VDcmVhdGVQYWdlIiwiYXBwQ29uZmlnQXRvbSIsInN0b3JlIiwidXNlQXRvbSIsInZvaWNlU3RvcmVBdG9tIiwia3kiLCJlbnYiLCJ2b2ljZXMiLCJmZXRjaFRUU1Byb3ZpZGVycyIsImFwaUtleSIsInJlc3BvbnNlIiwiZ2V0IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwib2siLCJFcnJvciIsInN0YXR1cyIsInJlc3VsdCIsImpzb24iLCJlcnIiLCJlcnJvclRleHQiLCJ0ZXh0IiwiZXJyb3JEYXRhIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJlcnJfY29kZSIsIkNyZWF0ZVBhZ2UiLCJzdGF0ZSIsInVwZGF0ZVNtYWxsU2VsZWN0IiwidXBkYXRlTGFyZ2VTZWxlY3QiLCJ1cGRhdGVUZXh0Q29udGVudCIsInVwZGF0ZUJhY2tncm91bmQiLCJ2b2ljZVN0b3JlIiwic2V0Vm9pY2VTdG9yZSIsInByb3ZpZGVyRGF0YSIsInByb3ZpZGVyX2xpc3QiLCJkb3ViYW9Qcm92aWRlciIsImZpbmQiLCJwIiwicHJvdmlkZXIiLCJ0b0xvd2VyQ2FzZSIsImRvdWJhb1ZvaWNlTGlzdCIsInJlcV9wYXJhbXNfaW5mbyIsInZvaWNlX2xpc3QiLCJkb3ViYW9Wb2ljZU9wdGlvbnMiLCJtYXAiLCJ2b2ljZSIsImtleSIsImxhYmVsIiwibmFtZSIsImdlbmRlciIsInQiLCJ2YWx1ZSIsIm9yaWdpbkRhdGEiLCJkb3ViYW9Wb2ljZSIsInYiLCJjaGlsZHJlbiIsImZpc2hQcm92aWRlciIsImZpc2hWb2ljZUxpc3QiLCJmaXNoVm9pY2VPcHRpb25zIiwiZmlzaFZvaWNlIiwibWluaW1heFByb3ZpZGVyIiwibWluaW1heFZvaWNlTGlzdCIsIm1pbmltYXhWb2ljZU9wdGlvbnMiLCJtaW5pbWF4Vm9pY2UiLCJvcGVuQWlQcm92aWRlciIsIm9wZW5BaVZvaWNlTGlzdCIsImFsbG93ZWRWb2ljZXMiLCJmaWx0ZXJlZE9wZW5BaVZvaWNlTGlzdCIsImZpbHRlciIsImluY2x1ZGVzIiwib3BlbkFpVm9pY2VPcHRpb25zIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsIm9wZW5BaVZvaWNlIiwicHJldiIsInZvaWNlTGlzdCIsImRpdiIsImNsYXNzTmFtZSIsImNvdmVySW1hZ2UiLCJjb3ZlckltYWdlVXJsIiwicGxhY2Vob2xkZXIiLCJzbWFsbFNlbGVjdFZhbHVlIiwibGFyZ2VTZWxlY3RWYWx1ZSIsInRleHRDb250ZW50Iiwib25TbWFsbFNlbGVjdENoYW5nZSIsIm9uTGFyZ2VTZWxlY3RDaGFuZ2UiLCJvblRleHRDaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx\n"));

/***/ })

});