{"c": ["app/[locale]/(main)/create/page", "app/[locale]/layout", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-scroll-area@1.2.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_kuuiiwhgyrbqx6frx7c6onkl7e/node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js", "(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx", "(app-pages-browser)/./src/components/ui/label.tsx", "(app-pages-browser)/./src/components/ui/scroll-area.tsx", "(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Ccode%5C%E6%95%B0%E5%AD%97%E4%BA%BA%5C302_starter%5Cnode_modules%5C.pnpm%5Cnext%4014.2.18_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js"]}