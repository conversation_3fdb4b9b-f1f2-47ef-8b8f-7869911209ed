"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx":
/*!*************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/aspect-ratio */ \"(app-pages-browser)/./src/components/ui/aspect-ratio.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _BackgroundSelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundSelector */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VideoPreviewFrame = (param)=>{\n    let { coverImage, aspectRatio = 16 / 9, placeholder = \"视频预览\", onBackgroundChange } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentBackground, setCurrentBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleImageError = ()=>{\n        setImageError(true);\n    };\n    const handleBackgroundSelect = (background)=>{\n        setCurrentBackground(background);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(background);\n    };\n    const getBackgroundStyle = ()=>{\n        if (!currentBackground) return {};\n        if (currentBackground.type === \"color\") {\n            return {\n                backgroundColor: currentBackground.value\n            };\n        } else if (currentBackground.type === \"preset\" || currentBackground.type === \"custom\") {\n            return {\n                backgroundImage: \"url(\".concat(currentBackground.value, \")\"),\n                backgroundSize: \"cover\",\n                backgroundPosition: \"center\",\n                backgroundRepeat: \"no-repeat\"\n            };\n        }\n        return {};\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__.AspectRatio, {\n                    ratio: aspectRatio,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border\",\n                        style: {\n                            ...getBackgroundStyle(),\n                            backgroundColor: currentBackground ? undefined : \"hsl(var(--muted))\"\n                        },\n                        children: coverImage && !imageError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: coverImage,\n                            alt: \"Video preview\",\n                            fill: true,\n                            className: \"rounded-md object-cover\",\n                            onError: handleImageError,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"z-10 flex flex-col items-center justify-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20 backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-8 w-8 text-muted-foreground\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"rounded bg-background/80 px-2 py-1 text-center text-sm text-muted-foreground backdrop-blur-sm\",\n                                    children: placeholder\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onBackgroundSelect: handleBackgroundSelect,\n                currentBackground: currentBackground || undefined\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoPreviewFrame, \"ZQMb4z8K6kVnrCpUuvkWnnMEmU0=\");\n_c = VideoPreviewFrame;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VideoPreviewFrame);\nvar _c;\n$RefreshReg$(_c, \"VideoPreviewFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\n"));

/***/ })

});