"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx":
/*!*************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/aspect-ratio */ \"(app-pages-browser)/./src/components/ui/aspect-ratio.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _background_select_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./background-select-modal */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/background-select-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VideoPreviewFrame = (param)=>{\n    let { coverImage, aspectRatio = 16 / 9, placeholder = \"视频预览\" } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleImageError = ()=>{\n        setImageError(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center gap-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__.AspectRatio, {\n                    ratio: aspectRatio,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border bg-muted\",\n                        children: coverImage && !imageError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: coverImage,\n                            alt: \"Video preview\",\n                            fill: true,\n                            className: \"rounded-md object-cover\",\n                            onError: handleImageError,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-8 w-8 text-muted-foreground\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-sm text-muted-foreground\",\n                                    children: placeholder\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                children: \"更换背景\"\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_background_select_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoPreviewFrame, \"gLR0P7wgc8ZXiun/rQPANvAzwwQ=\");\n_c = VideoPreviewFrame;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VideoPreviewFrame);\nvar _c;\n$RefreshReg$(_c, \"VideoPreviewFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/background-select-modal.tsx":
/*!*******************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/background-select-modal.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst BackgroundSelectModal = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"BackgroundSelectModal\"\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, undefined);\n};\n_c = BackgroundSelectModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BackgroundSelectModal);\nvar _c;\n$RefreshReg$(_c, \"BackgroundSelectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vKG1haW4pL2NyZWF0ZS9jb21wb25lbnRzL2JhY2tncm91bmQtc2VsZWN0LW1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUUxQixNQUFNQyx3QkFBd0I7SUFDNUIscUJBQU8sOERBQUNDO2tCQUFJOzs7Ozs7QUFDZDtLQUZNRDtBQUlOLCtEQUFlQSxxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9bbG9jYWxlXS8obWFpbikvY3JlYXRlL2NvbXBvbmVudHMvYmFja2dyb3VuZC1zZWxlY3QtbW9kYWwudHN4PzU2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuY29uc3QgQmFja2dyb3VuZFNlbGVjdE1vZGFsID0gKCkgPT4ge1xyXG4gIHJldHVybiA8ZGl2PkJhY2tncm91bmRTZWxlY3RNb2RhbDwvZGl2PjtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEJhY2tncm91bmRTZWxlY3RNb2RhbDtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQmFja2dyb3VuZFNlbGVjdE1vZGFsIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/background-select-modal.tsx\n"));

/***/ })

});