"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx":
/*!*************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/aspect-ratio */ \"(app-pages-browser)/./src/components/ui/aspect-ratio.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst VideoPreviewFrame = (param)=>{\n    let { coverImage, aspectRatio = 16 / 9, placeholder = \"视频预览\", onBackgroundChange } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentBackground, setCurrentBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleImageError = ()=>{\n        setImageError(true);\n    };\n    const handleBackgroundSelect = (background)=>{\n        setCurrentBackground(background);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(background);\n    };\n    const getBackgroundStyle = ()=>{\n        if (!currentBackground) return {};\n        if (currentBackground.type === \"color\") {\n            return {\n                backgroundColor: currentBackground.value\n            };\n        } else if (currentBackground.type === \"preset\" || currentBackground.type === \"custom\") {\n            return {\n                backgroundImage: \"url(\".concat(currentBackground.value, \")\"),\n                backgroundSize: \"cover\",\n                backgroundPosition: \"center\",\n                backgroundRepeat: \"no-repeat\"\n            };\n        }\n        return {};\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__.AspectRatio, {\n            ratio: aspectRatio,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border bg-muted\",\n                children: coverImage && !imageError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: coverImage,\n                    alt: \"Video preview\",\n                    fill: true,\n                    className: \"rounded-md object-cover\",\n                    onError: handleImageError,\n                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-8 w-8 text-muted-foreground\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-muted-foreground\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoPreviewFrame, \"ZQMb4z8K6kVnrCpUuvkWnnMEmU0=\");\n_c = VideoPreviewFrame;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VideoPreviewFrame);\nvar _c;\n$RefreshReg$(_c, \"VideoPreviewFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\n"));

/***/ })

});