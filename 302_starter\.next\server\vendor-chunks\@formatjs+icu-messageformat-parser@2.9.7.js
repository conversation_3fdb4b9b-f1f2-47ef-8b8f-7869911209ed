"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-messageformat-parser@2.9.7";
exports.ids = ["vendor-chunks/@formatjs+icu-messageformat-parser@2.9.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.11/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDTztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vMzAyLXN0YXJ0ZXIvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzPzk2N2YiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBTUEFDRV9TRVBBUkFUT1JfUkVHRVggPSAvWyBcXHhBMFxcdTE2ODBcXHUyMDAwLVxcdTIwMEFcXHUyMDJGXFx1MjA1RlxcdTMwMDBdLztcbmV4cG9ydCB2YXIgV0hJVEVfU1BBQ0VfUkVHRVggPSAvW1xcdC1cXHIgXFx4ODVcXHUyMDBFXFx1MjAwRlxcdTIwMjhcXHUyMDI5XS87XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvdGltZS1kYXRhLmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8zMDItc3RhcnRlci8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZm9ybWF0anMraWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyQDIuOS43L25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi90aW1lLWRhdGEuZ2VuZXJhdGVkLmpzPzc3MWEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHRpbWUtZGF0YS1nZW4udHNcbi8vIHByZXR0aWVyLWlnbm9yZSAgXG5leHBvcnQgdmFyIHRpbWVEYXRhID0ge1xuICAgIFwiMDAxXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIjQxOVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiQUNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFEXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBRVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiQUZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkFHXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBSVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQUxcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBTVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQU9cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJBU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJBVFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQVVcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFXXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBWFwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkFaXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiQkFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJCQlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQkRcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJCRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQkZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkJHXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiQkhcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkJJXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkJKXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQk1cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkJOXCI6IFtcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJCT1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiQlFcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJCUlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQlNcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkJUXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkJXXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCWVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJCWlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ0FcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNDXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDRFwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiQ0ZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDR1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ0hcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJDSVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ0tcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNMXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJDTVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNOXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJDT1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiQ1BcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJDUlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiQ1VcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkNWXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDV1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ1hcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNZXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDWlwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkRFXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJER1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiREpcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiREtcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJETVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiRE9cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkRaXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJFQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiRUNcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkVFXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJFR1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiRUhcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkVSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkVTXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJFVFwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiRklcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJGSlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiRktcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkZNXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJGT1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJGUlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR0FcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdCXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHRFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR0VcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJHRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR0dcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdIXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkdJXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJHTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR05cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdQXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHUVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiR1JcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdUXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJHVVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR1dcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdZXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJIS1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiSE5cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkhSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJIVVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJJQ1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiSURcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJJRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiSUxcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIklNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJJTlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJJT1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiSVFcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIklSXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJJU1wiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIklUXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJKRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiSk1cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkpPXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJKUFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcIktcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiS0VcIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIktHXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJLSFwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiS0lcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIktNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJLTlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiS1BcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIktSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJLV1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiS1lcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIktaXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJMQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTEJcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkxDXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJMSVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkxLXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJMUlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTFNcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTFRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkxVXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTFZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkxZXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJNQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiTUNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1EXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1GXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNR1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJNSFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTUtcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1MXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTU1cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1OXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNT1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTVBcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1RXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNUlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTVNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1UXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1VXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1WXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1XXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNWFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiTVlcIjogW1xuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIk1aXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJOQVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiTkNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5FXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTkZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5HXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJOSVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiTkxcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5PXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk5QXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTlJcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5VXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJOWlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiT01cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlBBXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJQRVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiUEZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJQR1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJQSFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUEtcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJQTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJQTVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiUE5cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlBSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJQU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUFRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlBXXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlBZXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJRQVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUkVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlJPXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJSU1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlJVXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUldcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiU0FcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNCXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTQ1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNEXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJTRVwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNHXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTSFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU0lcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNKXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiU0tcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJTTFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU01cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTTlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNPXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU1RcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNWXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJTWFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU1lcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNaXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJUQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVENcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlREXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVEZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJUR1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVEhcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVEpcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVExcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlRNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlROXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJUT1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJUUlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVFRcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlRXXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJUWlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVUFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJVR1wiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVU1cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlVTXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJVWVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiVVpcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJWQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlZDXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJWRVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiVkdcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlZJXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJWTlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJWVVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJXRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiV1NcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiWEtcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJZRVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiWVRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlpBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJaTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiWldcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiYWYtWkFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcImFyLTAwMVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiY2EtRVNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJlbi0wMDFcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcImVuLUhLXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJlbi1JTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiZW4tTVlcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcImVzLUJSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJlcy1FU1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiZXMtR1FcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcImZyLUNBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiZ2wtRVNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJndS1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiaGktSU5cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJpdC1DSFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIml0LUlUXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwia24tSU5cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJtbC1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIm1yLUlOXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJwYS1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwidGEtSU5cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcInRlLUlOXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwienUtWkFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsSUFBSTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0JBQW9CO0FBQ2Q7QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQztBQUN2QztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLzMwMi1zdGFydGVyLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXJAMi45Ljcvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3R5cGVzLmpzPzY3OTAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBUWVBFO1xuKGZ1bmN0aW9uIChUWVBFKSB7XG4gICAgLyoqXG4gICAgICogUmF3IHRleHRcbiAgICAgKi9cbiAgICBUWVBFW1RZUEVbXCJsaXRlcmFsXCJdID0gMF0gPSBcImxpdGVyYWxcIjtcbiAgICAvKipcbiAgICAgKiBWYXJpYWJsZSB3L28gYW55IGZvcm1hdCwgZS5nIGB2YXJgIGluIGB0aGlzIGlzIGEge3Zhcn1gXG4gICAgICovXG4gICAgVFlQRVtUWVBFW1wiYXJndW1lbnRcIl0gPSAxXSA9IFwiYXJndW1lbnRcIjtcbiAgICAvKipcbiAgICAgKiBWYXJpYWJsZSB3LyBudW1iZXIgZm9ybWF0XG4gICAgICovXG4gICAgVFlQRVtUWVBFW1wibnVtYmVyXCJdID0gMl0gPSBcIm51bWJlclwiO1xuICAgIC8qKlxuICAgICAqIFZhcmlhYmxlIHcvIGRhdGUgZm9ybWF0XG4gICAgICovXG4gICAgVFlQRVtUWVBFW1wiZGF0ZVwiXSA9IDNdID0gXCJkYXRlXCI7XG4gICAgLyoqXG4gICAgICogVmFyaWFibGUgdy8gdGltZSBmb3JtYXRcbiAgICAgKi9cbiAgICBUWVBFW1RZUEVbXCJ0aW1lXCJdID0gNF0gPSBcInRpbWVcIjtcbiAgICAvKipcbiAgICAgKiBWYXJpYWJsZSB3LyBzZWxlY3QgZm9ybWF0XG4gICAgICovXG4gICAgVFlQRVtUWVBFW1wic2VsZWN0XCJdID0gNV0gPSBcInNlbGVjdFwiO1xuICAgIC8qKlxuICAgICAqIFZhcmlhYmxlIHcvIHBsdXJhbCBmb3JtYXRcbiAgICAgKi9cbiAgICBUWVBFW1RZUEVbXCJwbHVyYWxcIl0gPSA2XSA9IFwicGx1cmFsXCI7XG4gICAgLyoqXG4gICAgICogT25seSBwb3NzaWJsZSB3aXRoaW4gcGx1cmFsIGFyZ3VtZW50LlxuICAgICAqIFRoaXMgaXMgdGhlIGAjYCBzeW1ib2wgdGhhdCB3aWxsIGJlIHN1YnN0aXR1dGVkIHdpdGggdGhlIGNvdW50LlxuICAgICAqL1xuICAgIFRZUEVbVFlQRVtcInBvdW5kXCJdID0gN10gPSBcInBvdW5kXCI7XG4gICAgLyoqXG4gICAgICogWE1MLWxpa2UgdGFnXG4gICAgICovXG4gICAgVFlQRVtUWVBFW1widGFnXCJdID0gOF0gPSBcInRhZ1wiO1xufSkoVFlQRSB8fCAoVFlQRSA9IHt9KSk7XG5leHBvcnQgdmFyIFNLRUxFVE9OX1RZUEU7XG4oZnVuY3Rpb24gKFNLRUxFVE9OX1RZUEUpIHtcbiAgICBTS0VMRVRPTl9UWVBFW1NLRUxFVE9OX1RZUEVbXCJudW1iZXJcIl0gPSAwXSA9IFwibnVtYmVyXCI7XG4gICAgU0tFTEVUT05fVFlQRVtTS0VMRVRPTl9UWVBFW1wiZGF0ZVRpbWVcIl0gPSAxXSA9IFwiZGF0ZVRpbWVcIjtcbn0pKFNLRUxFVE9OX1RZUEUgfHwgKFNLRUxFVE9OX1RZUEUgPSB7fSkpO1xuLyoqXG4gKiBUeXBlIEd1YXJkc1xuICovXG5leHBvcnQgZnVuY3Rpb24gaXNMaXRlcmFsRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLmxpdGVyYWw7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNBcmd1bWVudEVsZW1lbnQoZWwpIHtcbiAgICByZXR1cm4gZWwudHlwZSA9PT0gVFlQRS5hcmd1bWVudDtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc051bWJlckVsZW1lbnQoZWwpIHtcbiAgICByZXR1cm4gZWwudHlwZSA9PT0gVFlQRS5udW1iZXI7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNEYXRlRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLmRhdGU7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNUaW1lRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLnRpbWU7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNTZWxlY3RFbGVtZW50KGVsKSB7XG4gICAgcmV0dXJuIGVsLnR5cGUgPT09IFRZUEUuc2VsZWN0O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzUGx1cmFsRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLnBsdXJhbDtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1BvdW5kRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLnBvdW5kO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzVGFnRWxlbWVudChlbCkge1xuICAgIHJldHVybiBlbC50eXBlID09PSBUWVBFLnRhZztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc051bWJlclNrZWxldG9uKGVsKSB7XG4gICAgcmV0dXJuICEhKGVsICYmIHR5cGVvZiBlbCA9PT0gJ29iamVjdCcgJiYgZWwudHlwZSA9PT0gU0tFTEVUT05fVFlQRS5udW1iZXIpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzRGF0ZVRpbWVTa2VsZXRvbihlbCkge1xuICAgIHJldHVybiAhIShlbCAmJiB0eXBlb2YgZWwgPT09ICdvYmplY3QnICYmIGVsLnR5cGUgPT09IFNLRUxFVE9OX1RZUEUuZGF0ZVRpbWUpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUxpdGVyYWxFbGVtZW50KHZhbHVlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogVFlQRS5saXRlcmFsLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVOdW1iZXJFbGVtZW50KHZhbHVlLCBzdHlsZSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFRZUEUubnVtYmVyLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgIHN0eWxlOiBzdHlsZSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlDO0FBQ0c7QUFDRjtBQUM2SDtBQUMvSjtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVEQUFlLFFBQVEsdURBQWU7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix1REFBZSxRQUFRLHdEQUFnQjtBQUN4RDtBQUNBO0FBQ0Esa0JBQWtCLHFEQUFhLFFBQVEscURBQWE7QUFDcEQsWUFBWSwwREFBa0I7QUFDOUI7QUFDQTtBQUNBLGlCQUFpQixvREFBWTtBQUM3QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ087QUFDUCwyQkFBMkI7QUFDM0IsV0FBVywrQ0FBUSxHQUFHLHVEQUF1RDtBQUM3RSxxQkFBcUIsMkNBQU07QUFDM0I7QUFDQSxnQ0FBZ0MsNkNBQVM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN3QjtBQUN4QjtBQUNPLGNBQWMsMkNBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8zMDItc3RhcnRlci8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZm9ybWF0anMraWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyQDIuOS43L25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9pbmRleC5qcz83OTBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IF9fYXNzaWduIH0gZnJvbSBcInRzbGliXCI7XG5pbXBvcnQgeyBFcnJvcktpbmQgfSBmcm9tICcuL2Vycm9yJztcbmltcG9ydCB7IFBhcnNlciB9IGZyb20gJy4vcGFyc2VyJztcbmltcG9ydCB7IGlzRGF0ZUVsZW1lbnQsIGlzRGF0ZVRpbWVTa2VsZXRvbiwgaXNOdW1iZXJFbGVtZW50LCBpc051bWJlclNrZWxldG9uLCBpc1BsdXJhbEVsZW1lbnQsIGlzU2VsZWN0RWxlbWVudCwgaXNUYWdFbGVtZW50LCBpc1RpbWVFbGVtZW50LCB9IGZyb20gJy4vdHlwZXMnO1xuZnVuY3Rpb24gcHJ1bmVMb2NhdGlvbihlbHMpIHtcbiAgICBlbHMuZm9yRWFjaChmdW5jdGlvbiAoZWwpIHtcbiAgICAgICAgZGVsZXRlIGVsLmxvY2F0aW9uO1xuICAgICAgICBpZiAoaXNTZWxlY3RFbGVtZW50KGVsKSB8fCBpc1BsdXJhbEVsZW1lbnQoZWwpKSB7XG4gICAgICAgICAgICBmb3IgKHZhciBrIGluIGVsLm9wdGlvbnMpIHtcbiAgICAgICAgICAgICAgICBkZWxldGUgZWwub3B0aW9uc1trXS5sb2NhdGlvbjtcbiAgICAgICAgICAgICAgICBwcnVuZUxvY2F0aW9uKGVsLm9wdGlvbnNba10udmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzTnVtYmVyRWxlbWVudChlbCkgJiYgaXNOdW1iZXJTa2VsZXRvbihlbC5zdHlsZSkpIHtcbiAgICAgICAgICAgIGRlbGV0ZSBlbC5zdHlsZS5sb2NhdGlvbjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICgoaXNEYXRlRWxlbWVudChlbCkgfHwgaXNUaW1lRWxlbWVudChlbCkpICYmXG4gICAgICAgICAgICBpc0RhdGVUaW1lU2tlbGV0b24oZWwuc3R5bGUpKSB7XG4gICAgICAgICAgICBkZWxldGUgZWwuc3R5bGUubG9jYXRpb247XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNUYWdFbGVtZW50KGVsKSkge1xuICAgICAgICAgICAgcHJ1bmVMb2NhdGlvbihlbC5jaGlsZHJlbik7XG4gICAgICAgIH1cbiAgICB9KTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZShtZXNzYWdlLCBvcHRzKSB7XG4gICAgaWYgKG9wdHMgPT09IHZvaWQgMCkgeyBvcHRzID0ge307IH1cbiAgICBvcHRzID0gX19hc3NpZ24oeyBzaG91bGRQYXJzZVNrZWxldG9uczogdHJ1ZSwgcmVxdWlyZXNPdGhlckNsYXVzZTogdHJ1ZSB9LCBvcHRzKTtcbiAgICB2YXIgcmVzdWx0ID0gbmV3IFBhcnNlcihtZXNzYWdlLCBvcHRzKS5wYXJzZSgpO1xuICAgIGlmIChyZXN1bHQuZXJyKSB7XG4gICAgICAgIHZhciBlcnJvciA9IFN5bnRheEVycm9yKEVycm9yS2luZFtyZXN1bHQuZXJyLmtpbmRdKTtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBBc3NpZ24gdG8gZXJyb3Igb2JqZWN0XG4gICAgICAgIGVycm9yLmxvY2F0aW9uID0gcmVzdWx0LmVyci5sb2NhdGlvbjtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBBc3NpZ24gdG8gZXJyb3Igb2JqZWN0XG4gICAgICAgIGVycm9yLm9yaWdpbmFsTWVzc2FnZSA9IHJlc3VsdC5lcnIubWVzc2FnZTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICAgIGlmICghKG9wdHMgPT09IG51bGwgfHwgb3B0cyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0cy5jYXB0dXJlTG9jYXRpb24pKSB7XG4gICAgICAgIHBydW5lTG9jYXRpb24ocmVzdWx0LnZhbCk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQudmFsO1xufVxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG4vLyBvbmx5IGZvciB0ZXN0aW5nXG5leHBvcnQgdmFyIF9QYXJzZXIgPSBQYXJzZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.11/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDTztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vMzAyLXN0YXJ0ZXIvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjkuNy9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzPzA0YjkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBTUEFDRV9TRVBBUkFUT1JfUkVHRVggPSAvWyBcXHhBMFxcdTE2ODBcXHUyMDAwLVxcdTIwMEFcXHUyMDJGXFx1MjA1RlxcdTMwMDBdLztcbmV4cG9ydCB2YXIgV0hJVEVfU1BBQ0VfUkVHRVggPSAvW1xcdC1cXHIgXFx4ODVcXHUyMDBFXFx1MjAwRlxcdTIwMjhcXHUyMDI5XS87XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.9.7/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ })

};
;