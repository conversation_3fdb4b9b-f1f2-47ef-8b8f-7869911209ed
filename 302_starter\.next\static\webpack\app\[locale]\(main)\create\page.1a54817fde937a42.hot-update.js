"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts":
/*!***************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/hooks/useCreatePage.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreatePage: function() { return /* binding */ useCreatePage; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useCreatePage = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        smallSelectValue: \"\",\n        largeSelectValue: \"\",\n        textContent: \"\",\n        coverImageUrl: undefined,\n        backgroundOption: undefined\n    });\n    const updateSmallSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                smallSelectValue: value\n            }));\n    };\n    const updateLargeSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                largeSelectValue: value\n            }));\n    };\n    const updateTextContent = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                textContent: value\n            }));\n    };\n    const updateCoverImage = (url)=>{\n        setState((prev)=>({\n                ...prev,\n                coverImageUrl: url\n            }));\n    };\n    const updateBackground = (background)=>{\n        setState((prev)=>({\n                ...prev,\n                backgroundOption: background\n            }));\n    };\n    return {\n        state,\n        updateSmallSelect,\n        updateLargeSelect,\n        updateTextContent,\n        updateCoverImage,\n        updateBackground\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\n"));

/***/ })

});