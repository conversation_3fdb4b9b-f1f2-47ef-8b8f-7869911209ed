"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/constants/voices.ts":
/*!*********************************!*\
  !*** ./src/constants/voices.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   voices: function() { return /* binding */ voices; }\n/* harmony export */ });\nconst voices = [\n    {\n        key: \"OpenAI\",\n        label: \"OpenAI\",\n        value: \"OpenAI\",\n        children: [\n            {\n                key: \"fable\",\n                label: \"fable\",\n                value: \"fable\"\n            },\n            {\n                key: \"alloy\",\n                label: \"alloy\",\n                value: \"alloy\"\n            },\n            {\n                key: \"echo\",\n                label: \"echo\",\n                value: \"echo\"\n            },\n            {\n                key: \"nova\",\n                label: \"nova\",\n                value: \"nova\"\n            },\n            {\n                key: \"shimmer\",\n                label: \"shimmer\",\n                value: \"shimmer\"\n            }\n        ]\n    },\n    {\n        key: \"Azure\",\n        label: \"Azure\",\n        value: \"Azure\",\n        children: []\n    },\n    {\n        key: \"Doubao\",\n        label: \"Doubao\",\n        value: \"Doubao\",\n        children: []\n    },\n    {\n        key: \"fish\",\n        label: \"FishAudio\",\n        value: \"fish\",\n        children: []\n    },\n    {\n        key: \"Minimaxi\",\n        label: \"Minimax\",\n        value: \"Minimaxi\",\n        children: []\n    },\n    // {\n    //   key: \"dubbingx\",\n    //   label: \"Dubbingx\",\n    //   value: \"dubbingx\",\n    //   children: [],\n    // },\n    // {\n    //   key: \"elevenlabs\",\n    //   label: \"ElevenLabs\",\n    //   value: \"elevenlabs\",\n    //   children: [],\n    // },\n    {\n        key: \"custom\",\n        label: \"Custom\",\n        value: \"custom\",\n        children: []\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/voices.ts\n"));

/***/ })

});