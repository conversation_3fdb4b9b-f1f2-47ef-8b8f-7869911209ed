"use client";
import React, { useState } from "react";
import Image from "next/image";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Card } from "@/components/ui/card";
import { VideoPreviewFrameProps } from "../types";
import { Button } from "@/components/ui/button";
import { BackgroundSelectModal } from "./background-select-modal";

const VideoPreviewFrame: React.FC<VideoPreviewFrameProps> = ({
  coverImage,
  aspectRatio = 16 / 9,
  placeholder = "视频预览",
}) => {
  const [imageError, setImageError] = useState(false);
  const [backgroundSelectModalOpen, setBackgroundSelectModalOpen] =
    useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="flex flex-col items-center gap-y-4">
      <Card className="w-full overflow-hidden">
        <AspectRatio ratio={aspectRatio}>
          <div className="flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border bg-muted">
            {coverImage && !imageError ? (
              <Image
                src={coverImage}
                alt="Video preview"
                fill
                className="rounded-md object-cover"
                onError={handleImageError}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw"
              />
            ) : (
              <div className="flex flex-col items-center justify-center space-y-2">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20">
                  <svg
                    className="h-8 w-8 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="text-center text-sm text-muted-foreground">
                  {placeholder}
                </p>
              </div>
            )}
          </div>
        </AspectRatio>
      </Card>
      <Button onClick={() => setBackgroundSelectModalOpen(true)}>
        更换背景
      </Button>
      <BackgroundSelectModal
        open={backgroundSelectModalOpen}
        onOpenChange={setBackgroundSelectModalOpen}
      />
    </div>
  );
};

export default VideoPreviewFrame;
