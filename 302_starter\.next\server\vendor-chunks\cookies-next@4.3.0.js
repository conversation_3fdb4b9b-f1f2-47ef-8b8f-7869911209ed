"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cookies-next@4.3.0";
exports.ids = ["vendor-chunks/cookies-next@4.3.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hasCookie = exports.deleteCookie = exports.setCookie = exports.getCookie = exports.getCookies = void 0;\nvar cookie_1 = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/.pnpm/cookie@0.7.2/node_modules/cookie/index.js\");\nvar isClientSide = function () { return typeof window !== 'undefined'; };\nvar isCookiesFromAppRouter = function (cookieStore) {\n    if (!cookieStore)\n        return false;\n    return ('getAll' in cookieStore &&\n        'set' in cookieStore &&\n        typeof cookieStore.getAll === 'function' &&\n        typeof cookieStore.set === 'function');\n};\nvar isContextFromAppRouter = function (context) {\n    return ((!!(context === null || context === void 0 ? void 0 : context.req) && 'cookies' in context.req && isCookiesFromAppRouter(context === null || context === void 0 ? void 0 : context.req.cookies)) ||\n        (!!(context === null || context === void 0 ? void 0 : context.res) && 'cookies' in context.res && isCookiesFromAppRouter(context === null || context === void 0 ? void 0 : context.res.cookies)) ||\n        (!!(context === null || context === void 0 ? void 0 : context.cookies) && isCookiesFromAppRouter(context.cookies())));\n};\nvar transformAppRouterCookies = function (cookies) {\n    var _cookies = {};\n    cookies.getAll().forEach(function (_a) {\n        var name = _a.name, value = _a.value;\n        _cookies[name] = value;\n    });\n    return _cookies;\n};\nvar stringify = function (value) {\n    try {\n        if (typeof value === 'string') {\n            return value;\n        }\n        var stringifiedValue = JSON.stringify(value);\n        return stringifiedValue;\n    }\n    catch (e) {\n        return value;\n    }\n};\nvar decode = function (str) {\n    if (!str)\n        return str;\n    return str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n};\nvar getCookies = function (options) {\n    if (isContextFromAppRouter(options)) {\n        if (options === null || options === void 0 ? void 0 : options.req) {\n            return transformAppRouterCookies(options.req.cookies);\n        }\n        if (options === null || options === void 0 ? void 0 : options.cookies) {\n            return transformAppRouterCookies(options.cookies());\n        }\n    }\n    var req;\n    // DefaultOptions['req] can be casted here because is narrowed by using the fn: isContextFromAppRouter\n    if (options)\n        req = options.req;\n    if (!isClientSide()) {\n        // if cookie-parser is used in project get cookies from ctx.req.cookies\n        // if cookie-parser isn't used in project get cookies from ctx.req.headers.cookie\n        if (req && req.cookies)\n            return req.cookies;\n        if (req && req.headers.cookie)\n            return (0, cookie_1.parse)(req.headers.cookie);\n        return {};\n    }\n    var _cookies = {};\n    var documentCookies = document.cookie ? document.cookie.split('; ') : [];\n    for (var i = 0, len = documentCookies.length; i < len; i++) {\n        var cookieParts = documentCookies[i].split('=');\n        var _cookie = cookieParts.slice(1).join('=');\n        var name_1 = cookieParts[0];\n        _cookies[name_1] = _cookie;\n    }\n    return _cookies;\n};\nexports.getCookies = getCookies;\nvar getCookie = function (key, options) {\n    var _cookies = (0, exports.getCookies)(options);\n    var value = _cookies[key];\n    if (value === undefined)\n        return undefined;\n    return decode(value);\n};\nexports.getCookie = getCookie;\nvar setCookie = function (key, data, options) {\n    if (isContextFromAppRouter(options)) {\n        var req = options.req, res = options.res, cookiesFn = options.cookies, restOptions = __rest(options, [\"req\", \"res\", \"cookies\"]);\n        var payload = __assign({ name: key, value: stringify(data) }, restOptions);\n        if (req) {\n            req.cookies.set(payload);\n        }\n        if (res) {\n            res.cookies.set(payload);\n        }\n        if (cookiesFn) {\n            cookiesFn().set(payload);\n        }\n        return;\n    }\n    var _cookieOptions;\n    var _req;\n    var _res;\n    if (options) {\n        // DefaultOptions can be casted here because the AppRouterMiddlewareOptions is narrowed using the fn: isContextFromAppRouter\n        var _a = options, req = _a.req, res = _a.res, _options = __rest(_a, [\"req\", \"res\"]);\n        _req = req;\n        _res = res;\n        _cookieOptions = _options;\n    }\n    var cookieStr = (0, cookie_1.serialize)(key, stringify(data), __assign({ path: '/' }, _cookieOptions));\n    if (!isClientSide()) {\n        if (_res && _req) {\n            var currentCookies = _res.getHeader('Set-Cookie');\n            if (!Array.isArray(currentCookies)) {\n                currentCookies = !currentCookies ? [] : [String(currentCookies)];\n            }\n            _res.setHeader('Set-Cookie', currentCookies.concat(cookieStr));\n            if (_req && _req.cookies) {\n                var _cookies = _req.cookies;\n                data === '' ? delete _cookies[key] : (_cookies[key] = stringify(data));\n            }\n            if (_req && _req.headers && _req.headers.cookie) {\n                var _cookies = (0, cookie_1.parse)(_req.headers.cookie);\n                data === '' ? delete _cookies[key] : (_cookies[key] = stringify(data));\n                _req.headers.cookie = Object.entries(_cookies).reduce(function (accum, item) {\n                    return accum.concat(\"\".concat(item[0], \"=\").concat(item[1], \";\"));\n                }, '');\n            }\n        }\n    }\n    else {\n        document.cookie = cookieStr;\n    }\n};\nexports.setCookie = setCookie;\nvar deleteCookie = function (key, options) {\n    return (0, exports.setCookie)(key, '', __assign(__assign({}, options), { maxAge: -1 }));\n};\nexports.deleteCookie = deleteCookie;\nvar hasCookie = function (key, options) {\n    if (!key)\n        return false;\n    var cookie = (0, exports.getCookies)(options);\n    return cookie.hasOwnProperty(key);\n};\nexports.hasCookie = hasCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hasCookie = exports.deleteCookie = exports.setCookie = exports.getCookie = exports.getCookies = void 0;\nvar cookie_1 = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/.pnpm/cookie@0.7.2/node_modules/cookie/index.js\");\nvar isClientSide = function () { return typeof window !== 'undefined'; };\nvar isCookiesFromAppRouter = function (cookieStore) {\n    if (!cookieStore)\n        return false;\n    return ('getAll' in cookieStore &&\n        'set' in cookieStore &&\n        typeof cookieStore.getAll === 'function' &&\n        typeof cookieStore.set === 'function');\n};\nvar isContextFromAppRouter = function (context) {\n    return ((!!(context === null || context === void 0 ? void 0 : context.req) && 'cookies' in context.req && isCookiesFromAppRouter(context === null || context === void 0 ? void 0 : context.req.cookies)) ||\n        (!!(context === null || context === void 0 ? void 0 : context.res) && 'cookies' in context.res && isCookiesFromAppRouter(context === null || context === void 0 ? void 0 : context.res.cookies)) ||\n        (!!(context === null || context === void 0 ? void 0 : context.cookies) && isCookiesFromAppRouter(context.cookies())));\n};\nvar transformAppRouterCookies = function (cookies) {\n    var _cookies = {};\n    cookies.getAll().forEach(function (_a) {\n        var name = _a.name, value = _a.value;\n        _cookies[name] = value;\n    });\n    return _cookies;\n};\nvar stringify = function (value) {\n    try {\n        if (typeof value === 'string') {\n            return value;\n        }\n        var stringifiedValue = JSON.stringify(value);\n        return stringifiedValue;\n    }\n    catch (e) {\n        return value;\n    }\n};\nvar decode = function (str) {\n    if (!str)\n        return str;\n    return str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n};\nvar getCookies = function (options) {\n    if (isContextFromAppRouter(options)) {\n        if (options === null || options === void 0 ? void 0 : options.req) {\n            return transformAppRouterCookies(options.req.cookies);\n        }\n        if (options === null || options === void 0 ? void 0 : options.cookies) {\n            return transformAppRouterCookies(options.cookies());\n        }\n    }\n    var req;\n    // DefaultOptions['req] can be casted here because is narrowed by using the fn: isContextFromAppRouter\n    if (options)\n        req = options.req;\n    if (!isClientSide()) {\n        // if cookie-parser is used in project get cookies from ctx.req.cookies\n        // if cookie-parser isn't used in project get cookies from ctx.req.headers.cookie\n        if (req && req.cookies)\n            return req.cookies;\n        if (req && req.headers.cookie)\n            return (0, cookie_1.parse)(req.headers.cookie);\n        return {};\n    }\n    var _cookies = {};\n    var documentCookies = document.cookie ? document.cookie.split('; ') : [];\n    for (var i = 0, len = documentCookies.length; i < len; i++) {\n        var cookieParts = documentCookies[i].split('=');\n        var _cookie = cookieParts.slice(1).join('=');\n        var name_1 = cookieParts[0];\n        _cookies[name_1] = _cookie;\n    }\n    return _cookies;\n};\nexports.getCookies = getCookies;\nvar getCookie = function (key, options) {\n    var _cookies = (0, exports.getCookies)(options);\n    var value = _cookies[key];\n    if (value === undefined)\n        return undefined;\n    return decode(value);\n};\nexports.getCookie = getCookie;\nvar setCookie = function (key, data, options) {\n    if (isContextFromAppRouter(options)) {\n        var req = options.req, res = options.res, cookiesFn = options.cookies, restOptions = __rest(options, [\"req\", \"res\", \"cookies\"]);\n        var payload = __assign({ name: key, value: stringify(data) }, restOptions);\n        if (req) {\n            req.cookies.set(payload);\n        }\n        if (res) {\n            res.cookies.set(payload);\n        }\n        if (cookiesFn) {\n            cookiesFn().set(payload);\n        }\n        return;\n    }\n    var _cookieOptions;\n    var _req;\n    var _res;\n    if (options) {\n        // DefaultOptions can be casted here because the AppRouterMiddlewareOptions is narrowed using the fn: isContextFromAppRouter\n        var _a = options, req = _a.req, res = _a.res, _options = __rest(_a, [\"req\", \"res\"]);\n        _req = req;\n        _res = res;\n        _cookieOptions = _options;\n    }\n    var cookieStr = (0, cookie_1.serialize)(key, stringify(data), __assign({ path: '/' }, _cookieOptions));\n    if (!isClientSide()) {\n        if (_res && _req) {\n            var currentCookies = _res.getHeader('Set-Cookie');\n            if (!Array.isArray(currentCookies)) {\n                currentCookies = !currentCookies ? [] : [String(currentCookies)];\n            }\n            _res.setHeader('Set-Cookie', currentCookies.concat(cookieStr));\n            if (_req && _req.cookies) {\n                var _cookies = _req.cookies;\n                data === '' ? delete _cookies[key] : (_cookies[key] = stringify(data));\n            }\n            if (_req && _req.headers && _req.headers.cookie) {\n                var _cookies = (0, cookie_1.parse)(_req.headers.cookie);\n                data === '' ? delete _cookies[key] : (_cookies[key] = stringify(data));\n                _req.headers.cookie = Object.entries(_cookies).reduce(function (accum, item) {\n                    return accum.concat(\"\".concat(item[0], \"=\").concat(item[1], \";\"));\n                }, '');\n            }\n        }\n    }\n    else {\n        document.cookie = cookieStr;\n    }\n};\nexports.setCookie = setCookie;\nvar deleteCookie = function (key, options) {\n    return (0, exports.setCookie)(key, '', __assign(__assign({}, options), { maxAge: -1 }));\n};\nexports.deleteCookie = deleteCookie;\nvar hasCookie = function (key, options) {\n    if (!key)\n        return false;\n    var cookie = (0, exports.getCookies)(options);\n    return cookie.hasOwnProperty(key);\n};\nexports.hasCookie = hasCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/cookies-next@4.3.0/node_modules/cookies-next/lib/index.js\n");

/***/ })

};
;