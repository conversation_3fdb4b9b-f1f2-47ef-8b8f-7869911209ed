"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.1.6_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3_oekrf2uur4jjtk634vahjf4xnq";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.1.6_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3_oekrf2uur4jjtk634vahjf4xnq"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1.6_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3_oekrf2uur4jjtk634vahjf4xnq/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.1.6_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3_oekrf2uur4jjtk634vahjf4xnq/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+_ctzoznpxtg7jhipchbfc6nexmu/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._oobxexfkwdkb7g7pxpxe3ejlki/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._t7gwi7scqmbfhcn6ac4lo6xmuy/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+re_ph2bvylu7wxwep4doav2d32bhi/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // packages/react/tooltip/src/Tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const skipDelayTimer = skipDelayTimerRef.current;\n        return ()=>window.clearTimeout(skipDelayTimer);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayed,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            setIsOpenDelayed(false);\n        }, []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            skipDelayTimerRef.current = window.setTimeout(()=>setIsOpenDelayed(true), skipDelayDuration);\n        }, [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((inTransit)=>{\n            isPointerInTransitRef.current = inTransit;\n        }, []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen = false, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: (open2)=>{\n            if (open2) {\n                providerContext.onOpen();\n                document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n            } else {\n                providerContext.onClose();\n            }\n            onOpenChange?.(open2);\n        }\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n    }, [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        wasOpenDelayedRef.current = false;\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = window.setTimeout(()=>{\n            wasOpenDelayedRef.current = true;\n            setOpen(true);\n            openTimerRef.current = 0;\n        }, delayDuration);\n    }, [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (openTimerRef.current) {\n                window.clearTimeout(openTimerRef.current);\n                openTimerRef.current = 0;\n            }\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (providerContext.isOpenDelayed) handleDelayedOpen();\n                else handleOpen();\n            }, [\n                providerContext.isOpenDelayed,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (disableHoverableContent) {\n                    handleClose();\n                } else {\n                    window.clearTimeout(openTimerRef.current);\n                    openTimerRef.current = 0;\n                }\n            }, [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>isPointerDownRef.current = false, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        setPointerGraceArea(null);\n        onPointerInTransitChange(false);\n    }, [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event, hoverTarget)=>{\n        const currentTarget = event.currentTarget;\n        const exitPoint = {\n            x: event.clientX,\n            y: event.clientY\n        };\n        const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n        const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n        const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n        const graceArea = getHull([\n            ...paddedExitPoints,\n            ...hoverTargetPoints\n        ]);\n        setPointerGraceArea(graceArea);\n        onPointerInTransitChange(true);\n    }, [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>handleRemoveGraceArea();\n    }, [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trigger && content) {\n            const handleTriggerLeave = (event)=>handleCreateGraceArea(event, content);\n            const handleContentLeave = (event)=>handleCreateGraceArea(event, trigger);\n            trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n            content.addEventListener(\"pointerleave\", handleContentLeave);\n            return ()=>{\n                trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                content.removeEventListener(\"pointerleave\", handleContentLeave);\n            };\n        }\n    }, [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (pointerGraceArea) {\n            const handleTrackPointerGrace = (event)=>{\n                const target = event.target;\n                const pointerPosition = {\n                    x: event.clientX,\n                    y: event.clientY\n                };\n                const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                if (hasEnteredTarget) {\n                    handleRemoveGraceArea();\n                } else if (isPointerOutsideGraceArea) {\n                    handleRemoveGraceArea();\n                    onClose();\n                }\n            };\n            document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n            return ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n        }\n    }, [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        document.addEventListener(TOOLTIP_OPEN, onClose);\n        return ()=>document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (context.trigger) {\n            const handleScroll = (event)=>{\n                const target = event.target;\n                if (target?.contains(context.trigger)) onClose();\n            };\n            window.addEventListener(\"scroll\", handleScroll, {\n                capture: true\n            });\n            return ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n        }\n    }, [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1.6_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3_oekrf2uur4jjtk634vahjf4xnq/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;