"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/(main)/create/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/index.ts\");\n/* harmony import */ var _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hooks/useCreatePage */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\");\n/* harmony import */ var _stores__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores */ \"(app-pages-browser)/./src/stores/index.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n/* harmony import */ var ky__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ky */ \"(app-pages-browser)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/env */ \"(app-pages-browser)/./src/env.ts\");\n/* harmony import */ var _constants_voices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/constants/voices */ \"(app-pages-browser)/./src/constants/voices.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nasync function fetchTTSProviders(apiKey) {\n    if (!apiKey) {\n        return;\n    }\n    try {\n        const response = await ky__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"\".concat(_env__WEBPACK_IMPORTED_MODULE_7__.env.NEXT_PUBLIC_API_URL, \"/302/tts/provider\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"API调用失败: \".concat(response.status));\n        }\n        const result = await response.json();\n        return result;\n    } catch (err) {\n        const errorText = await err.response.text();\n        const errorData = JSON.parse(errorText);\n        if (errorData.error && errorData.error.err_code) {\n        // toast.error(() => ErrorToast(errorData.error.err_code));\n        } else {\n        // toast.error(\"获取供应商失败\");\n        }\n    }\n}\nconst CreatePage = ()=>{\n    _s();\n    const { state, updateSmallSelect, updateLargeSelect, updateTextContent, updateBackground } = (0,_hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage)();\n    const { apiKey } = _stores__WEBPACK_IMPORTED_MODULE_5__.store.get(_stores__WEBPACK_IMPORTED_MODULE_5__.appConfigAtom);\n    const [voiceStore, setVoiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_6__.voiceStoreAtom);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (async ()=>{\n            const providerData = await fetchTTSProviders(apiKey);\n            if (!providerData) {\n                return;\n            }\n            const { provider_list } = providerData;\n            // 处理 doubao 数据\n            const doubaoProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"doubao\");\n            if (doubaoProvider) {\n                const doubaoVoiceList = doubaoProvider.req_params_info.voice_list || [];\n                const doubaoVoiceOptions = doubaoVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Doubao children\n                const doubaoVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Doubao\");\n                if (doubaoVoice) {\n                    doubaoVoice.children = doubaoVoiceOptions;\n                }\n            }\n            // 处理 fish 数据\n            const fishProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"fish\");\n            if (fishProvider) {\n                const fishVoiceList = fishProvider.req_params_info.voice_list || [];\n                const fishVoiceOptions = fishVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.name || voice.voice,\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 FishAudio children\n                const fishVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"fish\");\n                if (fishVoice) {\n                    fishVoice.children = fishVoiceOptions;\n                }\n            }\n            // 处理 minimax 数据\n            const minimaxProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"minimaxi\");\n            if (minimaxProvider) {\n                const minimaxVoiceList = minimaxProvider.req_params_info.voice_list || [];\n                const minimaxVoiceOptions = minimaxVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: \"\".concat(voice.name, \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\"),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 Minimax children\n                const minimaxVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"Minimaxi\");\n                if (minimaxVoice) {\n                    minimaxVoice.children = minimaxVoiceOptions;\n                }\n            }\n            // // 处理 dubbingxi 数据\n            // const dubbingxiProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"dubbingx\"\n            // );\n            // if (dubbingxiProvider) {\n            //   const dubbingxiVoiceList =\n            //     dubbingxiProvider.req_params_info.voice_list || [];\n            //   const dubbingxiVoiceOptions: VoiceOption[] = dubbingxiVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: `${voice.name} (${t(voice.gender.toLowerCase())})`,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 dubbingxi children\n            //   const dubbingxiVoice = voices.find((v) => v.key === \"dubbingx\");\n            //   if (dubbingxiVoice) {\n            //     dubbingxiVoice.children = dubbingxiVoiceOptions;\n            //   }\n            // }\n            // // 处理 elevenlabs 数据\n            // const elevenlabsProvider = provider_list.find(\n            //   (p) => p.provider.toLowerCase() === \"elevenlabs\"\n            // );\n            // if (elevenlabsProvider) {\n            //   const elevenlabsVoiceList =\n            //     elevenlabsProvider.req_params_info.voice_list || [];\n            //   const elevenlabsVoiceOptions: VoiceOption[] = elevenlabsVoiceList.map(\n            //     (voice) => ({\n            //       key: voice.voice,\n            //       label: voice.name || voice.voice,\n            //       value: voice.voice,\n            //       originData: voice,\n            //     })\n            //   );\n            //   // 更新 voices 中的 elevenlabs children\n            //   const elevenlabsVoice = voices.find((v) => v.key === \"elevenlabs\");\n            //   if (elevenlabsVoice) {\n            //     elevenlabsVoice.children = elevenlabsVoiceOptions;\n            //   }\n            // }\n            // 处理 openai 数据\n            const openAiProvider = provider_list.find((p)=>p.provider.toLowerCase() === \"openai\");\n            if (openAiProvider) {\n                const openAiVoiceList = openAiProvider.req_params_info.voice_list || [];\n                // 只保留指定的几个音色\n                const allowedVoices = [\n                    \"alloy\",\n                    \"echo\",\n                    \"fable\",\n                    \"onyx\",\n                    \"nova\",\n                    \"shimmer\"\n                ];\n                const filteredOpenAiVoiceList = openAiVoiceList.filter((voice)=>allowedVoices.includes(voice.voice.toLowerCase()));\n                const openAiVoiceOptions = filteredOpenAiVoiceList.map((voice)=>({\n                        key: voice.voice,\n                        label: voice.gender ? \"\".concat(voice.name.charAt(0).toUpperCase() + voice.name.slice(1), \" \").concat(voice.gender ? \"(\".concat(t(\"common.\".concat(voice.gender.toLowerCase())), \")\") : \"\") : voice.name.charAt(0).toUpperCase() + voice.name.slice(1),\n                        value: voice.voice,\n                        originData: voice\n                    }));\n                // 更新 voices 中的 openai children\n                const openAiVoice = _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices.find((v)=>v.key === \"OpenAI\");\n                if (openAiVoice) {\n                    openAiVoice.children = openAiVoiceOptions;\n                }\n            }\n            // 如果需要持久化，可以更新到 store\n            setVoiceStore((prev)=>({\n                    ...prev,\n                    voiceList: _constants_voices__WEBPACK_IMPORTED_MODULE_8__.voices\n                }));\n        })();\n    }, [\n        apiKey\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"mx-auto w-full max-w-6xl p-6 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full flex-col gap-6 lg:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-2/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.VideoPreviewFrame, {\n                            coverImage: state.coverImageUrl,\n                            placeholder: \"视频封面预览\",\n                            onBackgroundChange: updateBackground\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 lg:w-3/5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.ConfigurationPanel, {\n                            smallSelectValue: state.smallSelectValue,\n                            largeSelectValue: state.largeSelectValue,\n                            textContent: state.textContent,\n                            onSmallSelectChange: updateSmallSelect,\n                            onLargeSelectChange: updateLargeSelect,\n                            onTextChange: updateTextContent\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreatePage, \"cADQ2ZJO/vQf/R2Yf8trRuUrxMo=\", false, function() {\n    return [\n        _hooks_useCreatePage__WEBPACK_IMPORTED_MODULE_4__.useCreatePage,\n        jotai__WEBPACK_IMPORTED_MODULE_10__.useAtom\n    ];\n});\n_c = CreatePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreatePage);\nvar _c;\n$RefreshReg$(_c, \"CreatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/page.tsx\n"));

/***/ })

});