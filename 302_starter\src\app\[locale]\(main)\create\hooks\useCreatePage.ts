import { useState } from "react";
import { CreatePageState } from "../types";

export const useCreatePage = () => {
  const [state, setState] = useState<CreatePageState>({
    smallSelectValue: "",
    largeSelectValue: "",
    textContent: "",
    coverImageUrl: undefined,
  });

  const updateSmallSelect = (value: string) => {
    setState((prev) => ({ ...prev, smallSelectValue: value }));
  };

  const updateLargeSelect = (value: string) => {
    setState((prev) => ({ ...prev, largeSelectValue: value }));
  };

  const updateTextContent = (value: string) => {
    setState((prev) => ({ ...prev, textContent: value }));
  };

  const updateCoverImage = (url?: string) => {
    setState((prev) => ({ ...prev, coverImageUrl: url }));
  };

  return {
    state,
    updateSmallSelect,
    updateLargeSelect,
    updateTextContent,
    updateCoverImage,
  };
};
