"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/constants/voices.ts":
/*!*********************************!*\
  !*** ./src/constants/voices.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   voices: function() { return /* binding */ voices; }\n/* harmony export */ });\nconst voices = [\n    {\n        key: \"OpenAI\",\n        label: \"OpenAI\",\n        value: \"OpenAI\",\n        children: [\n            {\n                key: \"fable\",\n                label: \"fable\",\n                value: \"fable\"\n            },\n            {\n                key: \"alloy\",\n                label: \"alloy\",\n                value: \"alloy\"\n            },\n            {\n                key: \"echo\",\n                label: \"echo\",\n                value: \"echo\"\n            },\n            {\n                key: \"nova\",\n                label: \"nova\",\n                value: \"nova\"\n            },\n            {\n                key: \"shimmer\",\n                label: \"shimmer\",\n                value: \"shimmer\"\n            }\n        ]\n    },\n    {\n        key: \"Azure\",\n        label: \"Azure\",\n        value: \"Azure\",\n        children: []\n    },\n    {\n        key: \"Doubao\",\n        label: \"Doubao\",\n        value: \"Doubao\",\n        children: []\n    },\n    {\n        key: \"fish\",\n        label: \"FishAudio\",\n        value: \"fish\",\n        children: []\n    },\n    {\n        key: \"Minimaxi\",\n        label: \"Minimax\",\n        value: \"Minimaxi\",\n        children: []\n    },\n    // {\n    //   key: \"dubbingx\",\n    //   label: \"Dubbingx\",\n    //   value: \"dubbingx\",\n    //   children: [],\n    // },\n    // {\n    //   key: \"elevenlabs\",\n    //   label: \"ElevenLabs\",\n    //   value: \"elevenlabs\",\n    //   children: [],\n    // },\n    {\n        key: \"custom\",\n        label: \"Custom\",\n        value: \"custom\",\n        children: []\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/voices.ts\n"));

/***/ })

});