"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi";
exports.ids = ["vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+_ctzoznpxtg7jhipchbfc6nexmu/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_pqcwgs2cux2wt222h43njmaoda/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._t7gwi7scqmbfhcn6ac4lo6xmuy/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.6.1_@types+react@18.3.17_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ })

};
;