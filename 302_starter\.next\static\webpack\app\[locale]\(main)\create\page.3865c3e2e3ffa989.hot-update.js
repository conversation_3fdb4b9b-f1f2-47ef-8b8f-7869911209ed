"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts":
/*!***************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/hooks/useCreatePage.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreatePage: function() { return /* binding */ useCreatePage; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useCreatePage = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        smallSelectValue: \"\",\n        largeSelectValue: \"\",\n        textContent: \"\",\n        coverImageUrl: undefined\n    });\n    const updateSmallSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                smallSelectValue: value\n            }));\n    };\n    const updateLargeSelect = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                largeSelectValue: value\n            }));\n    };\n    const updateTextContent = (value)=>{\n        setState((prev)=>({\n                ...prev,\n                textContent: value\n            }));\n    };\n    const updateCoverImage = (url)=>{\n        setState((prev)=>({\n                ...prev,\n                coverImageUrl: url\n            }));\n    };\n    return {\n        state,\n        updateSmallSelect,\n        updateLargeSelect,\n        updateTextContent,\n        updateCoverImage\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/hooks/useCreatePage.ts\n"));

/***/ })

});