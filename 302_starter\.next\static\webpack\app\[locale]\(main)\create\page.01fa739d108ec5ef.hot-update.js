"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/Icon.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/Icon.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Icon; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ createLucideIcon; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)(iconName)), className),\n            ...props\n        });\n    });\n    Component.displayName = \"\".concat(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \**********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ defaultAttributes; }\n/* harmony export */ });\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTMuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9kZWZhdWx0QXR0cmlidXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7SUFBQSxJQUFlQSxvQkFBQTtJQUNiQyxPQUFPO0lBQ1BDLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxTQUFTO0lBQ1RDLE1BQU07SUFDTkMsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLGVBQWU7SUFDZkMsZ0JBQWdCO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvZGVmYXVsdEF0dHJpYnV0ZXMudHM/MzcwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XG4gIHhtbG5zOiAnaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnLFxuICB3aWR0aDogMjQsXG4gIGhlaWdodDogMjQsXG4gIHZpZXdCb3g6ICcwIDAgMjQgMjQnLFxuICBmaWxsOiAnbm9uZScsXG4gIHN0cm9rZTogJ2N1cnJlbnRDb2xvcicsXG4gIHN0cm9rZVdpZHRoOiAyLFxuICBzdHJva2VMaW5lY2FwOiAncm91bmQnLFxuICBzdHJva2VMaW5lam9pbjogJ3JvdW5kJyxcbn07XG4iXSwibmFtZXMiOlsiZGVmYXVsdEF0dHJpYnV0ZXMiLCJ4bWxucyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Image; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Image = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Image\", [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1m3agn\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"9\",\n            r: \"2\",\n            key: \"af1f0g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n            key: \"1xmnt7\"\n        }\n    ]\n]);\n //# sourceMappingURL=image.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js ***!
  \******************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Palette; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Palette = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Palette\", [\n    [\n        \"circle\",\n        {\n            cx: \"13.5\",\n            cy: \"6.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"1okk4w\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"17.5\",\n            cy: \"10.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"f64h9f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"8.5\",\n            cy: \"7.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"fotxhn\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"6.5\",\n            cy: \"12.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"qy21gx\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z\",\n            key: \"12rzf8\"\n        }\n    ]\n]);\n //# sourceMappingURL=palette.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js ***!
  \*****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upload; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Upload = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Upload\", [\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"17 8 12 3 7 8\",\n            key: \"t8dd8p\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"3\",\n            y2: \"15\",\n            key: \"widbto\"\n        }\n    ]\n]);\n //# sourceMappingURL=upload.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeClasses: function() { return /* binding */ mergeClasses; },\n/* harmony export */   toKebabCase: function() { return /* binding */ toKebabCase; }\n/* harmony export */ });\n/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && array.indexOf(className) === index;\n    }).join(\" \");\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 预设背景图片\nconst presetBackgrounds = [\n    {\n        id: \"preset-1\",\n        type: \"preset\",\n        value: \"/backgrounds/office.jpg\",\n        preview: \"/backgrounds/office.jpg\",\n        name: \"办公室\"\n    },\n    {\n        id: \"preset-2\",\n        type: \"preset\",\n        value: \"/backgrounds/studio.jpg\",\n        preview: \"/backgrounds/studio.jpg\",\n        name: \"演播室\"\n    },\n    {\n        id: \"preset-3\",\n        type: \"preset\",\n        value: \"/backgrounds/nature.jpg\",\n        preview: \"/backgrounds/nature.jpg\",\n        name: \"自然风景\"\n    },\n    {\n        id: \"preset-4\",\n        type: \"preset\",\n        value: \"/backgrounds/modern.jpg\",\n        preview: \"/backgrounds/modern.jpg\",\n        name: \"现代简约\"\n    }\n];\n// 预设颜色背景\nconst colorBackgrounds = [\n    {\n        id: \"color-1\",\n        type: \"color\",\n        value: \"#ffffff\",\n        preview: \"#ffffff\",\n        name: \"纯白\"\n    },\n    {\n        id: \"color-2\",\n        type: \"color\",\n        value: \"#000000\",\n        preview: \"#000000\",\n        name: \"纯黑\"\n    },\n    {\n        id: \"color-3\",\n        type: \"color\",\n        value: \"#3b82f6\",\n        preview: \"#3b82f6\",\n        name: \"蓝色\"\n    },\n    {\n        id: \"color-4\",\n        type: \"color\",\n        value: \"#10b981\",\n        preview: \"#10b981\",\n        name: \"绿色\"\n    },\n    {\n        id: \"color-5\",\n        type: \"color\",\n        value: \"#f59e0b\",\n        preview: \"#f59e0b\",\n        name: \"橙色\"\n    },\n    {\n        id: \"color-6\",\n        type: \"color\",\n        value: \"#8b5cf6\",\n        preview: \"#8b5cf6\",\n        name: \"紫色\"\n    }\n];\nconst BackgroundSelector = (param)=>{\n    let { onBackgroundSelect, currentBackground } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customImageUrl, setCustomImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"preset\");\n    const handleBackgroundSelect = (background)=>{\n        onBackgroundSelect(background);\n        setOpen(false);\n    };\n    const handleCustomImageSubmit = ()=>{\n        if (customImageUrl.trim()) {\n            const customBackground = {\n                id: \"custom-\".concat(Date.now()),\n                type: \"custom\",\n                value: customImageUrl.trim(),\n                preview: customImageUrl.trim(),\n                name: \"自定义图片\"\n            };\n            handleBackgroundSelect(customBackground);\n            setCustomImageUrl(\"\");\n        }\n    };\n    const renderPresetBackgrounds = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 gap-3\",\n            children: presetBackgrounds.map((bg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"cursor-pointer transition-all hover:ring-2 hover:ring-primary \".concat((currentBackground === null || currentBackground === void 0 ? void 0 : currentBackground.id) === bg.id ? \"ring-2 ring-primary\" : \"\"),\n                    onClick: ()=>handleBackgroundSelect(bg),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative aspect-video overflow-hidden rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: bg.preview,\n                                alt: bg.name,\n                                fill: true,\n                                className: \"object-cover\",\n                                sizes: \"(max-width: 768px) 50vw, 25vw\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm font-medium\",\n                                children: bg.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, bg.id, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 132,\n            columnNumber: 5\n        }, undefined);\n    const renderColorBackgrounds = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-3 gap-3\",\n            children: colorBackgrounds.map((bg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"cursor-pointer transition-all hover:ring-2 hover:ring-primary \".concat((currentBackground === null || currentBackground === void 0 ? void 0 : currentBackground.id) === bg.id ? \"ring-2 ring-primary\" : \"\"),\n                    onClick: ()=>handleBackgroundSelect(bg),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative aspect-square overflow-hidden rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full w-full\",\n                                style: {\n                                    backgroundColor: bg.preview\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm font-medium\",\n                                children: bg.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, bg.id, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 159,\n            columnNumber: 5\n        }, undefined);\n    const renderCustomUpload = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                            htmlFor: \"custom-url\",\n                            children: \"图片URL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                    id: \"custom-url\",\n                                    placeholder: \"请输入图片URL\",\n                                    value: customImageUrl,\n                                    onChange: (e)=>setCustomImageUrl(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleCustomImageSubmit,\n                                    disabled: !customImageUrl.trim(),\n                                    children: \"确定\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg border-2 border-dashed border-border p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"mx-auto mb-4 h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2 text-sm text-muted-foreground\",\n                            children: \"拖拽图片到此处或点击上传\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"支持 JPG、PNG、GIF 格式\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            className: \"mt-4\",\n                            children: \"选择文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 183,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: setOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"outline\",\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        \"更换背景\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                className: \"max-h-[80vh] max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: \"选择背景\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 rounded-lg bg-muted p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"preset\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"preset\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"预设背景\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"color\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"color\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"纯色背景\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"custom\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"custom\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"自定义\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"h-[400px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pr-4\",\n                                    children: [\n                                        activeTab === \"preset\" && renderPresetBackgrounds(),\n                                        activeTab === \"color\" && renderColorBackgrounds(),\n                                        activeTab === \"custom\" && renderCustomUpload()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BackgroundSelector, \"/Ov6Ko9uqGKPxnxZiRt/FYjMhrA=\");\n_c = BackgroundSelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BackgroundSelector);\nvar _c;\n$RefreshReg$(_c, \"BackgroundSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx":
/*!*************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/aspect-ratio */ \"(app-pages-browser)/./src/components/ui/aspect-ratio.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _BackgroundSelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundSelector */ \"(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VideoPreviewFrame = (param)=>{\n    let { coverImage, aspectRatio = 16 / 9, placeholder = \"视频预览\", onBackgroundChange } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentBackground, setCurrentBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleImageError = ()=>{\n        setImageError(true);\n    };\n    const handleBackgroundSelect = (background)=>{\n        setCurrentBackground(background);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(background);\n    };\n    const getBackgroundStyle = ()=>{\n        if (!currentBackground) return {};\n        if (currentBackground.type === \"color\") {\n            return {\n                backgroundColor: currentBackground.value\n            };\n        } else if (currentBackground.type === \"preset\" || currentBackground.type === \"custom\") {\n            return {\n                backgroundImage: \"url(\".concat(currentBackground.value, \")\"),\n                backgroundSize: \"cover\",\n                backgroundPosition: \"center\",\n                backgroundRepeat: \"no-repeat\"\n            };\n        }\n        return {};\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_aspect_ratio__WEBPACK_IMPORTED_MODULE_3__.AspectRatio, {\n                    ratio: aspectRatio,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-border\",\n                        style: {\n                            ...getBackgroundStyle(),\n                            backgroundColor: currentBackground ? undefined : \"hsl(var(--muted))\"\n                        },\n                        children: coverImage && !imageError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: coverImage,\n                            alt: \"Video preview\",\n                            fill: true,\n                            className: \"rounded-md object-cover\",\n                            onError: handleImageError,\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"z-10 flex flex-col items-center justify-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-16 w-16 items-center justify-center rounded-lg bg-muted-foreground/20 backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-8 w-8 text-muted-foreground\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"rounded bg-background/80 px-2 py-1 text-center text-sm text-muted-foreground backdrop-blur-sm\",\n                                    children: placeholder\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onBackgroundSelect: handleBackgroundSelect,\n                currentBackground: currentBackground\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\VideoPreviewFrame.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoPreviewFrame, \"ZQMb4z8K6kVnrCpUuvkWnnMEmU0=\");\n_c = VideoPreviewFrame;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VideoPreviewFrame);\nvar _c;\n$RefreshReg$(_c, \"VideoPreviewFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/VideoPreviewFrame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: function() { return /* binding */ Button; },\n/* harmony export */   buttonVariants: function() { return /* binding */ buttonVariants; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_1__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            icon: \"bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\",\n            roundIconSm: \"size-6 rounded-full\",\n            roundIconMd: \"size-8 rounded-full\",\n            roundIconLg: \"size-10 rounded-full\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, size, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Button;\nButton.displayName = \"Button\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Button$React.forwardRef\");\n$RefreshReg$(_c1, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: function() { return /* binding */ Dialog; },\n/* harmony export */   DialogClose: function() { return /* binding */ DialogClose; },\n/* harmony export */   DialogContent: function() { return /* binding */ DialogContent; },\n/* harmony export */   DialogDescription: function() { return /* binding */ DialogDescription; },\n/* harmony export */   DialogFooter: function() { return /* binding */ DialogFooter; },\n/* harmony export */   DialogHeader: function() { return /* binding */ DialogHeader; },\n/* harmony export */   DialogOverlay: function() { return /* binding */ DialogOverlay; },\n/* harmony export */   DialogPortal: function() { return /* binding */ DialogPortal; },\n/* harmony export */   DialogTitle: function() { return /* binding */ DialogTitle; },\n/* harmony export */   DialogTrigger: function() { return /* binding */ DialogTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-icons@1.3.2_react@18.3.1/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogTrigger,DialogClose,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\n});\n_c = DialogOverlay;\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = DialogContent;\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = DialogHeader;\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = DialogFooter;\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c5 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined);\n});\n_c6 = DialogTitle;\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c7 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\n});\n_c8 = DialogDescription;\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"DialogOverlay\");\n$RefreshReg$(_c1, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogContent\");\n$RefreshReg$(_c3, \"DialogHeader\");\n$RefreshReg$(_c4, \"DialogFooter\");\n$RefreshReg$(_c5, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c6, \"DialogTitle\");\n$RefreshReg$(_c7, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: function() { return /* binding */ Input; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0I7QUFFRTtBQUtqQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQWdDSTtRQUEvQixFQUFFQyxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPO0lBQzVCLHFCQUNFLDhEQUFDQztRQUNDRixNQUFNQTtRQUNORCxXQUFXSiw4Q0FBRUEsQ0FDWCw4VkFDQUk7UUFFRkQsS0FBS0E7UUFDSixHQUFHRyxLQUFLOzs7Ozs7QUFHZjs7QUFFRkwsTUFBTU8sV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXHJcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XHJcblxyXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIFwiZmxleCBoLTkgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy10cmFuc3BhcmVudCBweC0zIHB5LTEgdGV4dC1zbSBzaGFkb3ctc20gdHJhbnNpdGlvbi1jb2xvcnMgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMSBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxyXG4gICAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIH1cclxuKTtcclxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCI7XHJcblxyXG5leHBvcnQgeyBJbnB1dCB9O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUrQjtBQUN5QjtBQUNVO0FBRWpDO0FBRWpDLE1BQU1JLGdCQUFnQkYsNkRBQUdBLENBQ3ZCO0FBR0YsTUFBTUcsc0JBQVFMLDZDQUFnQixNQUk1QixRQUEwQk87UUFBekIsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU87eUJBQ3hCLDhEQUFDUix1REFBbUI7UUFDbEJNLEtBQUtBO1FBQ0xDLFdBQVdMLDhDQUFFQSxDQUFDQyxpQkFBaUJJO1FBQzlCLEdBQUdDLEtBQUs7Ozs7Ozs7O0FBR2JKLE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiO1xyXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiO1xyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcclxuXHJcbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXHJcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxyXG4pO1xyXG5cclxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxyXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcclxuICAgIHJlZj17cmVmfVxyXG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgLz5cclxuKSk7XHJcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZTtcclxuXHJcbmV4cG9ydCB7IExhYmVsIH07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: function() { return /* binding */ ScrollArea; },\n/* harmony export */   ScrollBar: function() { return /* binding */ ScrollBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-scroll-area@1.2.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_kuuiiwhgyrbqx6frx7c6onkl7e/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = ScrollArea;\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, orientation = \"vertical\", ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = ScrollBar;\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ScrollArea$React.forwardRef\");\n$RefreshReg$(_c1, \"ScrollArea\");\n$RefreshReg$(_c2, \"ScrollBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/scroll-area.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: function() { return /* binding */ Close; },\n/* harmony export */   Content: function() { return /* binding */ Content; },\n/* harmony export */   Description: function() { return /* binding */ Description; },\n/* harmony export */   Dialog: function() { return /* binding */ Dialog; },\n/* harmony export */   DialogClose: function() { return /* binding */ DialogClose; },\n/* harmony export */   DialogContent: function() { return /* binding */ DialogContent; },\n/* harmony export */   DialogDescription: function() { return /* binding */ DialogDescription; },\n/* harmony export */   DialogOverlay: function() { return /* binding */ DialogOverlay; },\n/* harmony export */   DialogPortal: function() { return /* binding */ DialogPortal; },\n/* harmony export */   DialogTitle: function() { return /* binding */ DialogTitle; },\n/* harmony export */   DialogTrigger: function() { return /* binding */ DialogTrigger; },\n/* harmony export */   Overlay: function() { return /* binding */ Overlay; },\n/* harmony export */   Portal: function() { return /* binding */ Portal; },\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   Title: function() { return /* binding */ Title; },\n/* harmony export */   Trigger: function() { return /* binding */ Trigger; },\n/* harmony export */   WarningProvider: function() { return /* binding */ WarningProvider; },\n/* harmony export */   createDialogScope: function() { return /* binding */ createDialogScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-context@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+_ctzoznpxtg7jhipchbfc6nexmu/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_pqcwgs2cux2wt222h43njmaoda/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._t7gwi7scqmbfhcn6ac4lo6xmuy/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.6.1_@types+react@18.3.17_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(app-pages-browser)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$();\n// packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    _s();\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\n_s(Dialog, \"/yuatmodaV7w4hyulEirYGxYW3g=\", false, function() {\n    return [\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId\n    ];\n});\n_c = Dialog;\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c1 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n}, \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n})), \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n});\n_c2 = DialogTrigger;\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    _s2();\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\n_s2(DialogPortal, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c3 = DialogPortal;\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s3((props, forwardedRef)=>{\n    _s3();\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c5 = DialogOverlay;\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s4((props, forwardedRef)=>{\n    _s4();\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c6 = DialogOverlayImpl;\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ _s5(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c7 = _s5((props, forwardedRef)=>{\n    _s5();\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c8 = DialogContent;\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s6((props, forwardedRef)=>{\n    _s6();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            var _context_triggerRef_current;\n            event.preventDefault();\n            (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n}, \"z0QlyWdXD1MaBi1+3AtBr2MuboI=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n}));\n_c9 = DialogContentModal;\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s7((props, forwardedRef)=>{\n    _s7();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            var _props_onCloseAutoFocus;\n            (_props_onCloseAutoFocus = props.onCloseAutoFocus) === null || _props_onCloseAutoFocus === void 0 ? void 0 : _props_onCloseAutoFocus.call(props, event);\n            if (!event.defaultPrevented) {\n                var _context_triggerRef_current;\n                if (!hasInteractedOutsideRef.current) (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            var _props_onInteractOutside, _context_triggerRef_current;\n            (_props_onInteractOutside = props.onInteractOutside) === null || _props_onInteractOutside === void 0 ? void 0 : _props_onInteractOutside.call(props, event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n}, \"YrVQPDdDfWR20TF4ZFjbVSADLNA=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c10 = DialogContentNonModal;\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s8((props, forwardedRef)=>{\n    _s8();\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n}, \"wjdue5beIiufJExexORWgo6pOh0=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards\n    ];\n}));\n_c11 = DialogContentImpl;\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ _s9(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c12 = _s9((props, forwardedRef)=>{\n    _s9();\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c13 = DialogTitle;\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ _s10(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c14 = _s10((props, forwardedRef)=>{\n    _s10();\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c15 = DialogDescription;\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ _s11(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c16 = _s11((props, forwardedRef)=>{\n    _s11();\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c17 = DialogClose;\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = (param)=>{\n    let { titleId } = param;\n    _s12();\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = \"`\".concat(titleWarningContext.contentName, \"` requires a `\").concat(titleWarningContext.titleName, \"` for the component to be accessible for screen reader users.\\n\\nIf you want to hide the `\").concat(titleWarningContext.titleName, \"`, you can wrap it with our VisuallyHidden component.\\n\\nFor more information, see https://radix-ui.com/primitives/docs/components/\").concat(titleWarningContext.docsSlug);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\n_s12(TitleWarning, \"GA0m2oeX5XXEaAUGtZZQs5ML670=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c18 = TitleWarning;\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = (param)=>{\n    let { contentRef, descriptionId } = param;\n    _s13();\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = \"Warning: Missing `Description` or `aria-describedby={undefined}` for {\".concat(descriptionWarningContext.contentName, \"}.\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _contentRef_current;\n        const describedById = (_contentRef_current = contentRef.current) === null || _contentRef_current === void 0 ? void 0 : _contentRef_current.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\n_s13(DescriptionWarning, \"udowy/X+0YeBLGtDTT18KC58FH0=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c19 = DescriptionWarning;\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogPortal\");\n$RefreshReg$(_c4, \"DialogOverlay$React.forwardRef\");\n$RefreshReg$(_c5, \"DialogOverlay\");\n$RefreshReg$(_c6, \"DialogOverlayImpl\");\n$RefreshReg$(_c7, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogContent\");\n$RefreshReg$(_c9, \"DialogContentModal\");\n$RefreshReg$(_c10, \"DialogContentNonModal\");\n$RefreshReg$(_c11, \"DialogContentImpl\");\n$RefreshReg$(_c12, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c13, \"DialogTitle\");\n$RefreshReg$(_c14, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c15, \"DialogDescription\");\n$RefreshReg$(_c16, \"DialogClose$React.forwardRef\");\n$RefreshReg$(_c17, \"DialogClose\");\n$RefreshReg$(_c18, \"TitleWarning\");\n$RefreshReg$(_c19, \"DescriptionWarning\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; },\n/* harmony export */   Root: function() { return /* binding */ Root; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // packages/react/label/src/Label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = (props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            var _props_onMouseDown;\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            (_props_onMouseDown = props.onMouseDown) === null || _props_onMouseDown === void 0 ? void 0 : _props_onMouseDown.call(props, event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\n_c1 = Label;\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: function() { return /* binding */ Presence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n// packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    _s();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState !== null && nextState !== void 0 ? nextState : state;\n    }, initialState);\n}\n_s(useStateMachine, \"skVOqNGrFQuDFh+lpttAJ2AZFeA=\");\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    _s1();\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\n_s1(Presence, \"uNryTcoDvJa4CrInYRt27opyun0=\", false, function() {\n    return [\n        usePresence,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs\n    ];\n});\n_c = Presence;\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    _s2();\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || (styles === null || styles === void 0 ? void 0 : styles.display) === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            var _node_ownerDocument_defaultView;\n            const ownerWindow = (_node_ownerDocument_defaultView = node.ownerDocument.defaultView) !== null && _node_ownerDocument_defaultView !== void 0 ? _node_ownerDocument_defaultView : window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\n_s2(usePresence, \"ncCWxmFAyU87e4PnaTkbrqgR834=\", false, function() {\n    return [\n        useStateMachine\n    ];\n});\nfunction getAnimationName(styles) {\n    return (styles === null || styles === void 0 ? void 0 : styles.animationName) || \"none\";\n}\nfunction getElementRef(element) {\n    var _Object_getOwnPropertyDescriptor, _Object_getOwnPropertyDescriptor1;\n    let getter = (_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(element.props, \"ref\")) === null || _Object_getOwnPropertyDescriptor === void 0 ? void 0 : _Object_getOwnPropertyDescriptor.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = (_Object_getOwnPropertyDescriptor1 = Object.getOwnPropertyDescriptor(element, \"ref\")) === null || _Object_getOwnPropertyDescriptor1 === void 0 ? void 0 : _Object_getOwnPropertyDescriptor1.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\nvar _c;\n$RefreshReg$(_c, \"Presence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-scroll-area@1.2.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_kuuiiwhgyrbqx6frx7c6onkl7e/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-scroll-area@1.2.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_kuuiiwhgyrbqx6frx7c6onkl7e/node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: function() { return /* binding */ Corner; },\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   ScrollArea: function() { return /* binding */ ScrollArea; },\n/* harmony export */   ScrollAreaCorner: function() { return /* binding */ ScrollAreaCorner; },\n/* harmony export */   ScrollAreaScrollbar: function() { return /* binding */ ScrollAreaScrollbar; },\n/* harmony export */   ScrollAreaThumb: function() { return /* binding */ ScrollAreaThumb; },\n/* harmony export */   ScrollAreaViewport: function() { return /* binding */ ScrollAreaViewport; },\n/* harmony export */   Scrollbar: function() { return /* binding */ Scrollbar; },\n/* harmony export */   Thumb: function() { return /* binding */ Thumb; },\n/* harmony export */   Viewport: function() { return /* binding */ Viewport; },\n/* harmony export */   createScrollAreaScope: function() { return /* binding */ createScrollAreaScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-context@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$(), _s14 = $RefreshSig$(), _s15 = $RefreshSig$(), _s16 = $RefreshSig$();\n// packages/react/scroll-area/src/ScrollArea.tsx\n\n\n\n\n\n\n\n\n\n\n// packages/react/scroll-area/src/useStateMachine.ts\n\nfunction useStateMachine(initialState, machine) {\n    _s();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState !== null && nextState !== void 0 ? nextState : state;\n    }, initialState);\n}\n_s(useStateMachine, \"skVOqNGrFQuDFh+lpttAJ2AZFeA=\");\n// packages/react/scroll-area/src/ScrollArea.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n}, \"05Ruyd+KVjVqRyLvo9TqB+FhiPE=\", false, function() {\n    return [\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs,\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection\n    ];\n})), \"05Ruyd+KVjVqRyLvo9TqB+FhiPE=\", false, function() {\n    return [\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs,\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection\n    ];\n});\n_c1 = ScrollArea;\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s2((props, forwardedRef)=>{\n    _s2();\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: \"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}\"\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n}, \"QJDvjtN21Lm1MiN/J0fr6IVZH/U=\", false, function() {\n    return [\n        useScrollAreaContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs\n    ];\n})), \"QJDvjtN21Lm1MiN/J0fr6IVZH/U=\", false, function() {\n    return [\n        useScrollAreaContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs\n    ];\n});\n_c3 = ScrollAreaViewport;\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s3((props, forwardedRef)=>{\n    _s3();\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n}, \"ElaxBlXBgCRSqup4Y+nPaKERxHs=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n})), \"ElaxBlXBgCRSqup4Y+nPaKERxHs=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n});\n_c5 = ScrollAreaScrollbar;\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s4((props, forwardedRef)=>{\n    _s4();\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n}, \"gfF2MbracCyGmZmEc/gD3TfGPf8=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n}));\n_c6 = ScrollAreaScrollbarHover;\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s5((props, forwardedRef)=>{\n    _s5();\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n}, \"yVEA36XrGNc1A200CnLBdTLOtN4=\", false, function() {\n    return [\n        useScrollAreaContext,\n        useDebounceCallback,\n        useStateMachine\n    ];\n}));\n_c7 = ScrollAreaScrollbarScroll;\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s6((props, forwardedRef)=>{\n    _s6();\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n}, \"+UkQTpAB2k/p/r2Qgr6Cj1xxgfE=\", false, function() {\n    return [\n        useScrollAreaContext,\n        useDebounceCallback,\n        useResizeObserver,\n        useResizeObserver\n    ];\n}));\n_c8 = ScrollAreaScrollbarAuto;\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s7((props, forwardedRef)=>{\n    _s7();\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = \"translate3d(\".concat(offset, \"px, 0, 0)\");\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = \"translate3d(0, \".concat(offset, \"px, 0)\");\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n}, \"Wd7YQJPkFaDORidn1lbBkAJJGis=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n}));\n_c9 = ScrollAreaScrollbarVisible;\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s8((props, forwardedRef)=>{\n    _s8();\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n}, \"niSywvla1yTEJaUKF3mXxe2aACc=\", false, function() {\n    return [\n        useScrollAreaContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs\n    ];\n}));\n_c10 = ScrollAreaScrollbarX;\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s9((props, forwardedRef)=>{\n    _s9();\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n}, \"niSywvla1yTEJaUKF3mXxe2aACc=\", false, function() {\n    return [\n        useScrollAreaContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs\n    ];\n}));\n_c11 = ScrollAreaScrollbarY;\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s10((props, forwardedRef)=>{\n    _s10();\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar === null || scrollbar === void 0 ? void 0 : scrollbar.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n}, \"M/srFQWNmuOPpxuB8Z41v/5HpJg=\", false, function() {\n    return [\n        useScrollAreaContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef,\n        useDebounceCallback,\n        useResizeObserver,\n        useResizeObserver,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef,\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef\n    ];\n}));\n_c12 = ScrollAreaScrollbarImpl;\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ _s11(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c13 = _s11((props, forwardedRef)=>{\n    _s11();\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n}, \"s6DmoIaTLdkuRlSQD2uCGrj+q/c=\", false, function() {\n    return [\n        useScrollbarContext\n    ];\n})), \"s6DmoIaTLdkuRlSQD2uCGrj+q/c=\", false, function() {\n    return [\n        useScrollbarContext\n    ];\n});\n_c14 = ScrollAreaThumb;\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s12((props, forwardedRef)=>{\n    _s12();\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = void 0;\n        }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n}, \"Q7ygzvKop+l5vwjxQaIJ0OJSHPw=\", false, function() {\n    return [\n        useScrollAreaContext,\n        useScrollbarContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs,\n        useDebounceCallback\n    ];\n}));\n_c15 = ScrollAreaThumbImpl;\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ _s13(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c16 = _s13((props, forwardedRef)=>{\n    _s13();\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n}, \"6oF7LR91xweil/RUwsJOHeHGHpM=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n})), \"6oF7LR91xweil/RUwsJOHeHGHpM=\", false, function() {\n    return [\n        useScrollAreaContext\n    ];\n});\n_c17 = ScrollAreaCorner;\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s14((props, forwardedRef)=>{\n    _s14();\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, ()=>{\n        var _context_scrollbarX;\n        const height2 = ((_context_scrollbarX = context.scrollbarX) === null || _context_scrollbarX === void 0 ? void 0 : _context_scrollbarX.offsetHeight) || 0;\n        context.onCornerHeightChange(height2);\n        setHeight(height2);\n    });\n    useResizeObserver(context.scrollbarY, ()=>{\n        var _context_scrollbarY;\n        const width2 = ((_context_scrollbarY = context.scrollbarY) === null || _context_scrollbarY === void 0 ? void 0 : _context_scrollbarY.offsetWidth) || 0;\n        context.onCornerWidthChange(width2);\n        setWidth(width2);\n    });\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n}, \"VPucvwShgdNHWGLOUHD+VLuHeLc=\", false, function() {\n    return [\n        useScrollAreaContext,\n        useResizeObserver,\n        useResizeObserver\n    ];\n}));\n_c18 = ScrollAreaCornerImpl;\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes) {\n    let dir = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"ltr\";\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes) {\n    let dir = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"ltr\";\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = function(node) {\n    let handler = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ()=>{};\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    _s15();\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\n_s15(useDebounceCallback, \"y9yjvzcSqsK0X+bsDW4f7EiNh+8=\", false, function() {\n    return [\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef\n    ];\n});\nfunction useResizeObserver(element, onResize) {\n    _s16();\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\n_s16(useResizeObserver, \"/Ap8uEYN0y92xlnU4jqBFTKYw/I=\", false, function() {\n    return [\n        _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef\n    ];\n});\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"ScrollArea$React2.forwardRef\");\n$RefreshReg$(_c1, \"ScrollArea\");\n$RefreshReg$(_c2, \"ScrollAreaViewport$React2.forwardRef\");\n$RefreshReg$(_c3, \"ScrollAreaViewport\");\n$RefreshReg$(_c4, \"ScrollAreaScrollbar$React2.forwardRef\");\n$RefreshReg$(_c5, \"ScrollAreaScrollbar\");\n$RefreshReg$(_c6, \"ScrollAreaScrollbarHover\");\n$RefreshReg$(_c7, \"ScrollAreaScrollbarScroll\");\n$RefreshReg$(_c8, \"ScrollAreaScrollbarAuto\");\n$RefreshReg$(_c9, \"ScrollAreaScrollbarVisible\");\n$RefreshReg$(_c10, \"ScrollAreaScrollbarX\");\n$RefreshReg$(_c11, \"ScrollAreaScrollbarY\");\n$RefreshReg$(_c12, \"ScrollAreaScrollbarImpl\");\n$RefreshReg$(_c13, \"ScrollAreaThumb$React2.forwardRef\");\n$RefreshReg$(_c14, \"ScrollAreaThumb\");\n$RefreshReg$(_c15, \"ScrollAreaThumbImpl\");\n$RefreshReg$(_c16, \"ScrollAreaCorner$React2.forwardRef\");\n$RefreshReg$(_c17, \"ScrollAreaCorner\");\n$RefreshReg$(_c18, \"ScrollAreaCornerImpl\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-scroll-area@1.2.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_kuuiiwhgyrbqx6frx7c6onkl7e/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cva: function() { return /* binding */ cva; },\n/* harmony export */   cx: function() { return /* binding */ cx; }\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ \nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nconst cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;\nconst cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\n"));

/***/ })

});