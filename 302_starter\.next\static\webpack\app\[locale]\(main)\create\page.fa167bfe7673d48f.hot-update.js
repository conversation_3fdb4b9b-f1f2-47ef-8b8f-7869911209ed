"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ConfigurationPanel = (param)=>{\n    let { onSmallSelectChange, onLargeSelectChange, onTextChange, smallSelectValue, largeSelectValue, textContent } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const [voiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__.voiceStoreAtom);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4 sm:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-none space-y-2 sm:w-[30%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: smallSelectValue,\n                            onValueChange: onSmallSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"small-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择语音平台\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: voiceStore.voiceList.map((voiceGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: voiceGroup.value,\n                                            children: voiceGroup.label\n                                        }, voiceGroup.key, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2 sm:w-[65%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: largeSelectValue,\n                            onValueChange: onLargeSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"large-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择声音模型\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: voiceStore.voiceList.map((voiceGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1.5 text-sm font-semibold text-muted-foreground\",\n                                                    children: voiceGroup.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                voiceGroup.children.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                        value: \"\".concat(voiceGroup.value, \":\").concat(voice.value),\n                                                        children: voice.label\n                                                    }, voice.key, false, {\n                                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, voiceGroup.key, true, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                        id: \"text-content\",\n                        placeholder: \"请输入AI数字人需要说的内容...\",\n                        value: textContent,\n                        onChange: (e)=>onTextChange(e.target.value),\n                        className: \"min-h-[120px] resize-y focus:border-transparent focus:ring-2 focus:ring-primary\",\n                        rows: 5\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConfigurationPanel, \"9WOyjciEqfcrB6BNgulNUNXvYQg=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom\n    ];\n});\n_c = ConfigurationPanel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ConfigurationPanel);\nvar _c;\n$RefreshReg$(_c, \"ConfigurationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx\n"));

/***/ })

});