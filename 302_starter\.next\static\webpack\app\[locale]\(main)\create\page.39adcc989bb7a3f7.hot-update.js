"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/background-select-modal.tsx":
/*!*******************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/background-select-modal.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundSelectModal: function() { return /* binding */ BackgroundSelectModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\n\n\n\n\nfunction BackgroundSelectModal() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"outline\",\n                        children: \"Open Dialog\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                    children: \"Edit profile\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                    children: \"Make changes to your profile here. Click save when you're done.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"name-1\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"name-1\",\n                                            name: \"name\",\n                                            defaultValue: \"Pedro Duarte\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"username-1\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"username-1\",\n                                            name: \"username\",\n                                            defaultValue: \"@peduarte\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogClose, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    type: \"submit\",\n                                    children: \"Save changes\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\background-select-modal.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = BackgroundSelectModal;\nvar _c;\n$RefreshReg$(_c, \"BackgroundSelectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/background-select-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: function() { return /* binding */ Dialog; },\n/* harmony export */   DialogClose: function() { return /* binding */ DialogClose; },\n/* harmony export */   DialogContent: function() { return /* binding */ DialogContent; },\n/* harmony export */   DialogDescription: function() { return /* binding */ DialogDescription; },\n/* harmony export */   DialogFooter: function() { return /* binding */ DialogFooter; },\n/* harmony export */   DialogHeader: function() { return /* binding */ DialogHeader; },\n/* harmony export */   DialogOverlay: function() { return /* binding */ DialogOverlay; },\n/* harmony export */   DialogPortal: function() { return /* binding */ DialogPortal; },\n/* harmony export */   DialogTitle: function() { return /* binding */ DialogTitle; },\n/* harmony export */   DialogTrigger: function() { return /* binding */ DialogTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-icons@1.3.2_react@18.3.1/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogTrigger,DialogClose,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\n});\n_c = DialogOverlay;\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = DialogContent;\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = DialogHeader;\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = DialogFooter;\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c5 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined);\n});\n_c6 = DialogTitle;\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c7 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\n});\n_c8 = DialogDescription;\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"DialogOverlay\");\n$RefreshReg$(_c1, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogContent\");\n$RefreshReg$(_c3, \"DialogHeader\");\n$RefreshReg$(_c4, \"DialogFooter\");\n$RefreshReg$(_c5, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c6, \"DialogTitle\");\n$RefreshReg$(_c7, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: function() { return /* binding */ Input; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0I7QUFFRTtBQUtqQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQWdDSTtRQUEvQixFQUFFQyxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPO0lBQzVCLHFCQUNFLDhEQUFDQztRQUNDRixNQUFNQTtRQUNORCxXQUFXSiw4Q0FBRUEsQ0FDWCw4VkFDQUk7UUFFRkQsS0FBS0E7UUFDSixHQUFHRyxLQUFLOzs7Ozs7QUFHZjs7QUFFRkwsTUFBTU8sV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXHJcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XHJcblxyXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIFwiZmxleCBoLTkgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy10cmFuc3BhcmVudCBweC0zIHB5LTEgdGV4dC1zbSBzaGFkb3ctc20gdHJhbnNpdGlvbi1jb2xvcnMgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMSBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxyXG4gICAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIH1cclxuKTtcclxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCI7XHJcblxyXG5leHBvcnQgeyBJbnB1dCB9O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUrQjtBQUN5QjtBQUNVO0FBRWpDO0FBRWpDLE1BQU1JLGdCQUFnQkYsNkRBQUdBLENBQ3ZCO0FBR0YsTUFBTUcsc0JBQVFMLDZDQUFnQixNQUk1QixRQUEwQk87UUFBekIsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU87eUJBQ3hCLDhEQUFDUix1REFBbUI7UUFDbEJNLEtBQUtBO1FBQ0xDLFdBQVdMLDhDQUFFQSxDQUFDQyxpQkFBaUJJO1FBQzlCLEdBQUdDLEtBQUs7Ozs7Ozs7O0FBR2JKLE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiO1xyXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiO1xyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcclxuXHJcbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXHJcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxyXG4pO1xyXG5cclxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxyXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcclxuICAgIHJlZj17cmVmfVxyXG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgLz5cclxuKSk7XHJcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZTtcclxuXHJcbmV4cG9ydCB7IExhYmVsIH07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: function() { return /* binding */ Close; },\n/* harmony export */   Content: function() { return /* binding */ Content; },\n/* harmony export */   Description: function() { return /* binding */ Description; },\n/* harmony export */   Dialog: function() { return /* binding */ Dialog; },\n/* harmony export */   DialogClose: function() { return /* binding */ DialogClose; },\n/* harmony export */   DialogContent: function() { return /* binding */ DialogContent; },\n/* harmony export */   DialogDescription: function() { return /* binding */ DialogDescription; },\n/* harmony export */   DialogOverlay: function() { return /* binding */ DialogOverlay; },\n/* harmony export */   DialogPortal: function() { return /* binding */ DialogPortal; },\n/* harmony export */   DialogTitle: function() { return /* binding */ DialogTitle; },\n/* harmony export */   DialogTrigger: function() { return /* binding */ DialogTrigger; },\n/* harmony export */   Overlay: function() { return /* binding */ Overlay; },\n/* harmony export */   Portal: function() { return /* binding */ Portal; },\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   Title: function() { return /* binding */ Title; },\n/* harmony export */   Trigger: function() { return /* binding */ Trigger; },\n/* harmony export */   WarningProvider: function() { return /* binding */ WarningProvider; },\n/* harmony export */   createDialogScope: function() { return /* binding */ createDialogScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-context@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+_ctzoznpxtg7jhipchbfc6nexmu/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@_pqcwgs2cux2wt222h43njmaoda/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.3_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._t7gwi7scqmbfhcn6ac4lo6xmuy/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.6.1_@types+react@18.3.17_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(app-pages-browser)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$();\n// packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    _s();\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\n_s(Dialog, \"/yuatmodaV7w4hyulEirYGxYW3g=\", false, function() {\n    return [\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId\n    ];\n});\n_c = Dialog;\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c1 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n}, \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n})), \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n});\n_c2 = DialogTrigger;\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    _s2();\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\n_s2(DialogPortal, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c3 = DialogPortal;\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s3((props, forwardedRef)=>{\n    _s3();\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c5 = DialogOverlay;\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s4((props, forwardedRef)=>{\n    _s4();\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c6 = DialogOverlayImpl;\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ _s5(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c7 = _s5((props, forwardedRef)=>{\n    _s5();\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c8 = DialogContent;\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s6((props, forwardedRef)=>{\n    _s6();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            var _context_triggerRef_current;\n            event.preventDefault();\n            (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n}, \"z0QlyWdXD1MaBi1+3AtBr2MuboI=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n}));\n_c9 = DialogContentModal;\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s7((props, forwardedRef)=>{\n    _s7();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            var _props_onCloseAutoFocus;\n            (_props_onCloseAutoFocus = props.onCloseAutoFocus) === null || _props_onCloseAutoFocus === void 0 ? void 0 : _props_onCloseAutoFocus.call(props, event);\n            if (!event.defaultPrevented) {\n                var _context_triggerRef_current;\n                if (!hasInteractedOutsideRef.current) (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            var _props_onInteractOutside, _context_triggerRef_current;\n            (_props_onInteractOutside = props.onInteractOutside) === null || _props_onInteractOutside === void 0 ? void 0 : _props_onInteractOutside.call(props, event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n}, \"YrVQPDdDfWR20TF4ZFjbVSADLNA=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c10 = DialogContentNonModal;\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s8((props, forwardedRef)=>{\n    _s8();\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n}, \"wjdue5beIiufJExexORWgo6pOh0=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards\n    ];\n}));\n_c11 = DialogContentImpl;\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ _s9(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c12 = _s9((props, forwardedRef)=>{\n    _s9();\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c13 = DialogTitle;\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ _s10(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c14 = _s10((props, forwardedRef)=>{\n    _s10();\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c15 = DialogDescription;\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ _s11(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c16 = _s11((props, forwardedRef)=>{\n    _s11();\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c17 = DialogClose;\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = (param)=>{\n    let { titleId } = param;\n    _s12();\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = \"`\".concat(titleWarningContext.contentName, \"` requires a `\").concat(titleWarningContext.titleName, \"` for the component to be accessible for screen reader users.\\n\\nIf you want to hide the `\").concat(titleWarningContext.titleName, \"`, you can wrap it with our VisuallyHidden component.\\n\\nFor more information, see https://radix-ui.com/primitives/docs/components/\").concat(titleWarningContext.docsSlug);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\n_s12(TitleWarning, \"GA0m2oeX5XXEaAUGtZZQs5ML670=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c18 = TitleWarning;\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = (param)=>{\n    let { contentRef, descriptionId } = param;\n    _s13();\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = \"Warning: Missing `Description` or `aria-describedby={undefined}` for {\".concat(descriptionWarningContext.contentName, \"}.\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _contentRef_current;\n        const describedById = (_contentRef_current = contentRef.current) === null || _contentRef_current === void 0 ? void 0 : _contentRef_current.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\n_s13(DescriptionWarning, \"udowy/X+0YeBLGtDTT18KC58FH0=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c19 = DescriptionWarning;\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogPortal\");\n$RefreshReg$(_c4, \"DialogOverlay$React.forwardRef\");\n$RefreshReg$(_c5, \"DialogOverlay\");\n$RefreshReg$(_c6, \"DialogOverlayImpl\");\n$RefreshReg$(_c7, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogContent\");\n$RefreshReg$(_c9, \"DialogContentModal\");\n$RefreshReg$(_c10, \"DialogContentNonModal\");\n$RefreshReg$(_c11, \"DialogContentImpl\");\n$RefreshReg$(_c12, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c13, \"DialogTitle\");\n$RefreshReg$(_c14, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c15, \"DialogDescription\");\n$RefreshReg$(_c16, \"DialogClose$React.forwardRef\");\n$RefreshReg$(_c17, \"DialogClose\");\n$RefreshReg$(_c18, \"TitleWarning\");\n$RefreshReg$(_c19, \"DescriptionWarning\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.4_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3._mzqifyg4fqbbjqnoldv6wpavbi/node_modules/@radix-ui/react-dialog/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; },\n/* harmony export */   Root: function() { return /* binding */ Root; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18_x63sf3zoos6eg4shxl32xrxebi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // packages/react/label/src/Label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = (props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            var _props_onMouseDown;\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            (_props_onMouseDown = props.onMouseDown) === null || _props_onMouseDown === void 0 ? void 0 : _props_onMouseDown.call(props, event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\n_c1 = Label;\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18.3.1_i7lj5scsrebg3wdpyj5ipvbvai/node_modules/@radix-ui/react-label/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: function() { return /* binding */ Presence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.1_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@18.3.17_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n// packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    _s();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState !== null && nextState !== void 0 ? nextState : state;\n    }, initialState);\n}\n_s(useStateMachine, \"skVOqNGrFQuDFh+lpttAJ2AZFeA=\");\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    _s1();\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\n_s1(Presence, \"uNryTcoDvJa4CrInYRt27opyun0=\", false, function() {\n    return [\n        usePresence,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs\n    ];\n});\n_c = Presence;\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    _s2();\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || (styles === null || styles === void 0 ? void 0 : styles.display) === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            var _node_ownerDocument_defaultView;\n            const ownerWindow = (_node_ownerDocument_defaultView = node.ownerDocument.defaultView) !== null && _node_ownerDocument_defaultView !== void 0 ? _node_ownerDocument_defaultView : window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\n_s2(usePresence, \"ncCWxmFAyU87e4PnaTkbrqgR834=\", false, function() {\n    return [\n        useStateMachine\n    ];\n});\nfunction getAnimationName(styles) {\n    return (styles === null || styles === void 0 ? void 0 : styles.animationName) || \"none\";\n}\nfunction getElementRef(element) {\n    var _Object_getOwnPropertyDescriptor, _Object_getOwnPropertyDescriptor1;\n    let getter = (_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(element.props, \"ref\")) === null || _Object_getOwnPropertyDescriptor === void 0 ? void 0 : _Object_getOwnPropertyDescriptor.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = (_Object_getOwnPropertyDescriptor1 = Object.getOwnPropertyDescriptor(element, \"ref\")) === null || _Object_getOwnPropertyDescriptor1 === void 0 ? void 0 : _Object_getOwnPropertyDescriptor1.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\nvar _c;\n$RefreshReg$(_c, \"Presence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.2_@types+react-dom@18.3.5_@types+react@18.3.17__@types+react@18._syigirhiv7ym4o5bgbgbyx4yji/node_modules/@radix-ui/react-presence/dist/index.mjs\n"));

/***/ })

});