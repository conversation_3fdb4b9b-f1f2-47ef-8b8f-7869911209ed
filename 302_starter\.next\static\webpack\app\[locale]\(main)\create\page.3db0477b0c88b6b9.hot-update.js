"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Palette,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.453.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 预设背景图片 - 使用 Unsplash 占位符图片\nconst presetBackgrounds = [\n    {\n        id: \"preset-1\",\n        type: \"preset\",\n        value: \"https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=450&fit=crop\",\n        preview: \"https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=225&fit=crop\",\n        name: \"办公室\"\n    },\n    {\n        id: \"preset-2\",\n        type: \"preset\",\n        value: \"https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=800&h=450&fit=crop\",\n        preview: \"https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=225&fit=crop\",\n        name: \"演播室\"\n    },\n    {\n        id: \"preset-3\",\n        type: \"preset\",\n        value: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=450&fit=crop\",\n        preview: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop\",\n        name: \"自然风景\"\n    },\n    {\n        id: \"preset-4\",\n        type: \"preset\",\n        value: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=450&fit=crop\",\n        preview: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=225&fit=crop\",\n        name: \"现代简约\"\n    }\n];\n// 预设颜色背景\nconst colorBackgrounds = [\n    {\n        id: \"color-1\",\n        type: \"color\",\n        value: \"#ffffff\",\n        preview: \"#ffffff\",\n        name: \"纯白\"\n    },\n    {\n        id: \"color-2\",\n        type: \"color\",\n        value: \"#000000\",\n        preview: \"#000000\",\n        name: \"纯黑\"\n    },\n    {\n        id: \"color-3\",\n        type: \"color\",\n        value: \"#3b82f6\",\n        preview: \"#3b82f6\",\n        name: \"蓝色\"\n    },\n    {\n        id: \"color-4\",\n        type: \"color\",\n        value: \"#10b981\",\n        preview: \"#10b981\",\n        name: \"绿色\"\n    },\n    {\n        id: \"color-5\",\n        type: \"color\",\n        value: \"#f59e0b\",\n        preview: \"#f59e0b\",\n        name: \"橙色\"\n    },\n    {\n        id: \"color-6\",\n        type: \"color\",\n        value: \"#8b5cf6\",\n        preview: \"#8b5cf6\",\n        name: \"紫色\"\n    }\n];\nconst BackgroundSelector = (param)=>{\n    let { onBackgroundSelect, currentBackground } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customImageUrl, setCustomImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"preset\");\n    const handleBackgroundSelect = (background)=>{\n        onBackgroundSelect(background);\n        setOpen(false);\n    };\n    const handleCustomImageSubmit = ()=>{\n        if (customImageUrl.trim()) {\n            const customBackground = {\n                id: \"custom-\".concat(Date.now()),\n                type: \"custom\",\n                value: customImageUrl.trim(),\n                preview: customImageUrl.trim(),\n                name: \"自定义图片\"\n            };\n            handleBackgroundSelect(customBackground);\n            setCustomImageUrl(\"\");\n        }\n    };\n    const renderPresetBackgrounds = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 gap-3\",\n            children: presetBackgrounds.map((bg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"cursor-pointer transition-all hover:ring-2 hover:ring-primary \".concat((currentBackground === null || currentBackground === void 0 ? void 0 : currentBackground.id) === bg.id ? \"ring-2 ring-primary\" : \"\"),\n                    onClick: ()=>handleBackgroundSelect(bg),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative aspect-video overflow-hidden rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: bg.preview,\n                                alt: bg.name,\n                                fill: true,\n                                className: \"object-cover\",\n                                sizes: \"(max-width: 768px) 50vw, 25vw\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm font-medium\",\n                                children: bg.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, bg.id, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 140,\n            columnNumber: 5\n        }, undefined);\n    const renderColorBackgrounds = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-3 gap-3\",\n            children: colorBackgrounds.map((bg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"cursor-pointer transition-all hover:ring-2 hover:ring-primary \".concat((currentBackground === null || currentBackground === void 0 ? void 0 : currentBackground.id) === bg.id ? \"ring-2 ring-primary\" : \"\"),\n                    onClick: ()=>handleBackgroundSelect(bg),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative aspect-square overflow-hidden rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full w-full\",\n                                style: {\n                                    backgroundColor: bg.preview\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm font-medium\",\n                                children: bg.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, bg.id, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 167,\n            columnNumber: 5\n        }, undefined);\n    const renderCustomUpload = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                            htmlFor: \"custom-url\",\n                            children: \"图片URL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                    id: \"custom-url\",\n                                    placeholder: \"请输入图片URL\",\n                                    value: customImageUrl,\n                                    onChange: (e)=>setCustomImageUrl(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleCustomImageSubmit,\n                                    disabled: !customImageUrl.trim(),\n                                    children: \"确定\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg border-2 border-dashed border-border p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"mx-auto mb-4 h-12 w-12 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2 text-sm text-muted-foreground\",\n                            children: \"拖拽图片到此处或点击上传\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"支持 JPG、PNG、GIF 格式\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            className: \"mt-4\",\n                            children: \"选择文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: setOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"outline\",\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        \"更换背景\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                className: \"max-h-[80vh] max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: \"选择背景\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 rounded-lg bg-muted p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"preset\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"preset\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"预设背景\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"color\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"color\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"纯色背景\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: activeTab === \"custom\" ? \"default\" : \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setActiveTab(\"custom\"),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Palette_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"自定义\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"h-[400px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pr-4\",\n                                    children: [\n                                        activeTab === \"preset\" && renderPresetBackgrounds(),\n                                        activeTab === \"color\" && renderColorBackgrounds(),\n                                        activeTab === \"custom\" && renderCustomUpload()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\BackgroundSelector.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BackgroundSelector, \"/Ov6Ko9uqGKPxnxZiRt/FYjMhrA=\");\n_c = BackgroundSelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BackgroundSelector);\nvar _c;\n$RefreshReg$(_c, \"BackgroundSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/BackgroundSelector.tsx\n"));

/***/ })

});