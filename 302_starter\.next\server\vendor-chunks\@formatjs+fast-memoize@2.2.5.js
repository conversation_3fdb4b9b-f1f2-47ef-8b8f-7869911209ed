"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+fast-memoize@2.2.5";
exports.ids = ["vendor-chunks/@formatjs+fast-memoize@2.2.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.5/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ })

};
;